const colors = require('./src/components/ui/colors');

module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter'],
        PoppinsBlack: ['Poppins-Black'], //900
        PoppinsBlackI: ['Poppins-BlackItalic'], //900
        PoppinsExtraBold: ['Poppins-ExtraBold'], //800
        PoppinsExtraBoldI: ['Poppins-ExtraBoldItalic'], //800
        PoppinsBold: ['Poppins-Bold'], //700
        PoppinsBoldI: ['Poppins-BoldItalic'], //700
        PoppinsSemiBold: ['Poppins-SemiBold'], //600
        PoppinsSemiBoldI: ['Poppins-SemiBoldItalic'], //600
        PoppinsMedium: ['Poppins-Medium'], //500
        PoppinsMediumI: ['Poppins-MediumItalic'], //500
        PoppinsRegular: ['Poppins-Regular'], //400
        PoppinsRegularI: ['Poppins-Italic'], //400
        PoppinsLight: ['Poppins-Light'], //300
        PoppinsLightI: ['Poppins-LightItalic'], //300
        PoppinsExtraLight: ['Poppins-ExtraLight'], //200
        PoppinsExtraLightI: ['Poppins-ExtraLightItalic'], //200
        PoppinsThin: ['Poppins-Thin'], //100
        PoppinsThinI: ['Poppins-ThinItalic'], //100
      },
      colors,
      borderWidth: {
        1: '1px',
        0.5: '0.5px',
      },
    },
  },
  plugins: [],
};
