/* eslint-disable max-lines-per-function */
import type { ConfigContext, ExpoConfig } from '@expo/config';
import type { AppIconBadgeConfig } from 'app-icon-badge/types';

import { ClientEnv, Env } from './env';

const appIconBadgeConfig: AppIconBadgeConfig = {
  enabled: Env.APP_ENV !== 'production',
  badges: [
    {
      text: Env.APP_ENV,
      type: 'banner',
      color: 'white',
    },
    {
      text: Env.VERSION.toString(),
      type: 'ribbon',
      color: 'white',
    },
  ],
};

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Thedal',
  description: `${Env.NAME}`,
  owner: 'nextfly-icloud', // Env.EXPO_ACCOUNT_OWNER,
  scheme: 'com.nextflytech.thedal',
  slug: 'thedal',
  version: '0.0.3',
  orientation: 'portrait',
  icon: './assets/icon2.png',
  userInterfaceStyle: 'automatic',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'cover',
    backgroundColor: '#2E3C4B',
  },
  // runtimeVersion: {
  //   policy: 'appVersion',
  // },
  updates: {
    url: 'https://u.expo.dev/2cc6ab1d-e6f8-4941-94d5-6fb1f69822a3',
    fallbackToCacheTimeout: 0,
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.nextflytech.thedal',
    runtimeVersion: '0.0.3',
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      CFBundleAllowMixedLocalizations: true,
      CFBundleLocalizations: ['en'],
      NSCameraUsageDescription:
        'The app accesses your camera to let you take photos.',
      NSPhotoLibraryUsageDescription:
        'The app accesses your photos to let you share them.',
      NSPhotoLibraryAddUsageDescription:
        'The app accesses your photo library to save photos.',
    },
  },
  experiments: {
    typedRoutes: true,
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon2.png',
      backgroundColor: '#ffffff',
    },
    package: 'com.nextflytech.thedal',
    runtimeVersion: '0.0.3',
    permissions: [
      'CAMERA',
      'READ_EXTERNAL_STORAGE',
      'WRITE_EXTERNAL_STORAGE',
      'READ_MEDIA_IMAGES',
    ],
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },
  plugins: [
    [
      'expo-font',
      {
        fonts: [
          './assets/fonts/Inter.ttf',
          './assets/fonts/Poppins-Black.ttf',
          './assets/fonts/Poppins-BlackItalic.ttf',
          './assets/fonts/Poppins-Bold.ttf',
          './assets/fonts/Poppins-BoldItalic.ttf',
          './assets/fonts/Poppins-ExtraBold.ttf',
          './assets/fonts/Poppins-ExtraBoldItalic.ttf',
          './assets/fonts/Poppins-ExtraLight.ttf',
          './assets/fonts/Poppins-ExtraLightItalic.ttf',
          './assets/fonts/Poppins-Italic.ttf',
          './assets/fonts/Poppins-Light.ttf',
          './assets/fonts/Poppins-LightItalic.ttf',
          './assets/fonts/Poppins-Medium.ttf',
          './assets/fonts/Poppins-MediumItalic.ttf',
          './assets/fonts/Poppins-Regular.ttf',
          './assets/fonts/Poppins-SemiBold.ttf',
          './assets/fonts/Poppins-SemiBoldItalic.ttf',
          './assets/fonts/Poppins-Thin.ttf',
          './assets/fonts/Poppins-ThinItalic.ttf',
        ],
      },
    ],
    'expo-localization',
    'expo-router',
    [
      'expo-build-properties',
      {
        android: {
          kotlinVersion: '1.7.22',
        },
      },
    ],
    [
      'app-icon-badge',
      {
        enabled: Env.APP_ENV !== 'production',
        badges: [
          {
            text: Env.APP_ENV,
            type: 'banner',
            color: 'white',
          },
          {
            text: Env.VERSION.toString(),
            type: 'ribbon',
            color: 'white',
          },
        ],
      },
    ],
    [
      'expo-document-picker',
      {
        iCloudContainerEnvironment: 'Production',
      },
    ],
    [
      'expo-calendar',
      {
        calendarPermission: 'The app needs to access your calendar.',
      },
    ],
    ['app-icon-badge', appIconBadgeConfig],
    ['react-native-edge-to-edge'],
    [
      '@stripe/stripe-react-native',
      {
        // "merchantIdentifier": string | string [],
        // "enableGooglePay": boolean
      },
    ],
    'expo-web-browser',
  ],
  extra: {
    ...ClientEnv,
    eas: {
      projectId: '2cc6ab1d-e6f8-4941-94d5-6fb1f69822a3',
    },
  },
});
