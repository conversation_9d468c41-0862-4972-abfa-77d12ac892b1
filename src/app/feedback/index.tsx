import { Env } from '@env';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Controller, type FieldErrors, useForm } from 'react-hook-form';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { ID } from 'react-native-appwrite';
import { showMessage } from 'react-native-flash-message';
import StarRating from 'react-native-star-rating-widget';

import BackButton from '@/components/back-button';
import InputText from '@/components/input-txt';
import { Button } from '@/components/ui';
import { databases } from '@/lib/appwrite';

type SelectionOptionProps = {
  options: string[];
  title: string;
  onSelect: (option: string) => void;
  selectedOption: string | null;
};

const SelectionOptions = ({
  options,
  title,
  onSelect,
  selectedOption,
}: SelectionOptionProps) => {
  return (
    <View className="gap-2.5">
      <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 dark:text-black-50">
        {title}
      </Text>
      <View className="flex-row flex-wrap items-center gap-4">
        {options.map((option) => (
          <Pressable
            key={option}
            onPress={() => onSelect(option)}
            className={`rounded-[12px] px-[18px] py-2.5 ${
              selectedOption === option ? 'bg-primary-50' : 'bg-primary-900'
            }`}
          >
            <Text
              className={`font-inter text-[14px] font-normal leading-5 ${
                selectedOption === option
                  ? 'text-blue'
                  : 'text-black-100 dark:text-black-100'
              }`}
            >
              {option}
            </Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
};

export default function Feedback() {
  const [rating, setRating] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitted },
  } = useForm<{
    likedOption: string;
    improvementOption: string;
    additionalFeedback: string;
  }>({
    defaultValues: {
      likedOption: '',
      improvementOption: '',
      additionalFeedback: '',
    },
  });

  const likeOptions = ['Easy to use', 'Helpful', 'Better Prices', 'Convenient'];
  const improvementOptions = [
    'Better design',
    'Transaction Issue',
    'Spam User',
    'Violent User',
    'Others',
  ];

  const onSubmit = async (data: {
    likedOption: string;
    improvementOption: string;
    additionalFeedback: string;
  }) => {
    console.log('Form submitted:', { ...data, rating });
    if (rating === 0) {
      showMessage({
        message: 'Please fill all the fields',
        type: 'danger',
      });
    }
    // Handle form submission logic here
    try {
      setIsLoading(true);
      const feedback = await databases.createDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_FEEDBACK,
        ID.unique(),
        { ...data, rating }
      );
      console.log('feedback is', feedback);
      showMessage({
        message: 'Feedback sent successfully',
        type: 'success',
      });
      router.dismissTo('/profile');
    } catch (error) {
      console.log('error in sending feedback', error);
      showMessage({
        message: 'Error in sending feedback',
        type: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onError = (
    error: FieldErrors<{
      likedOption: string;
      improvementOption: string;
      feedback: string;
    }>
  ) => {
    console.log('Form errors:', error);
    showMessage({
      message: 'Please fill all the fields',
      type: 'danger',
    });
  };

  return (
    <View className="w-full flex-1">
      <View className="bg-darkcardbg px-5 py-3">
        <BackButton
          text="Back"
          text2="Give Your Feedback "
          onPress={() => {
            router.back();
          }}
        />
      </View>
      <ScrollView className="flex-1 ">
        <View className=" flex-1 gap-8 bg-primary-950 px-5">
          {/* Row 1 */}
          <View className="mt-8 gap-2.5">
            <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 text-black-0">
              Rate our App
            </Text>

            <StarRating rating={rating} onChange={setRating} starSize={30} />
            {rating === 0 && isSubmitted && (
              <Text className="font-inter text-xs text-danger-50">
                Please rate our app
              </Text>
            )}
          </View>

          {/* Row 2 - What did you like */}
          <Controller
            control={control}
            name="likedOption"
            rules={{ required: 'Please select what you liked' }}
            render={({ field: { onChange, value } }) => (
              <SelectionOptions
                title="What did you like about Thedal App?"
                options={likeOptions}
                onSelect={onChange}
                selectedOption={value}
              />
            )}
          />
          {errors.likedOption && (
            <Text className="-mt-6 font-inter text-xs text-danger-50">
              {errors.likedOption.message}
            </Text>
          )}

          {/* Row 3 - What could be improved */}
          <Controller
            control={control}
            name="improvementOption"
            rules={{ required: 'Please select an improvement area' }}
            render={({ field: { onChange, value } }) => (
              <SelectionOptions
                title="What could be improved?"
                options={improvementOptions}
                onSelect={onChange}
                selectedOption={value}
              />
            )}
          />
          {errors.improvementOption && (
            <Text className="-mt-6 font-inter text-xs text-danger-50">
              {errors.improvementOption.message}
            </Text>
          )}

          {/* Row 4 */}
          <Controller
            control={control}
            name="additionalFeedback"
            render={({ field: { onChange, value } }) => (
              <View className="gap-2.5">
                <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 dark:text-black-50">
                  Additional feedback
                </Text>
                <InputText
                  placeholder="Share your thoughts here..."
                  // value={value}
                  onChange={(text) => {
                    onChange(text.nativeEvent.text);
                    console.log('text is', text.nativeEvent.text);
                  }}
                  multiline={true}
                  numberOfLines={3}
                  textAlignVertical="top"
                  className="min-h-[120px] align-top"
                />
              </View>
            )}
          />
        </View>
      </ScrollView>
      <View className="mb-8 px-5">
        <Button
          variant="secondary"
          label="Send Feedback"
          loading={isLoading}
          onPress={handleSubmit(onSubmit, onError)}
        />
      </View>
    </View>
  );
}
