import { useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';

import { account } from '@/lib/appwrite';
import { getUserData } from '@/lib/user-data-service';
import { userTypeStore } from '@/store/user-type-store';

export default function Index() {
  console.log('Landing Page');
  const router = useRouter();

  // Get the changeUser function from the store outside of the component render
  const changeUser = userTypeStore.getState().changeUser;

  const getUserDetails = async () => {
    try {
      const user = await account.get();
      const userData = await getUserData();
      console.log('userData', userData);

      // Update the user in the store
      if (user) {
        changeUser(user);
      }

      if (!user) {
        router.push('/onboarding');
      }

      if (!userData?.companionProfile && !userData?.travellerProfile) {
        console.log('pushing welcome');
        router.push('/welcome');
        return;
      }
      console.log('pushing home');
      router.push('/(app)/home');
    } catch (e) {
      router.push('/onboarding');
    }

    // const userDetails = await databases.getDocument(
    //   '6711d07f002f03a35d08',
    //   '675aaac0000029cc416c',
    //   userType === UserType.TRAVELLER ? user.$id : `c-${user.$id}`
    // );
    // console.log('user details areeeee', userDetails);
    // if (userDetails.photos) setImage(userDetails.photos);
    // setTraveller(userDetails.typeOfTraveller);
  };

  useEffect(() => {
    getUserDetails();
  }, []);

  return (
    <View className="w-full flex-1 items-center justify-center">
      <ActivityIndicator color="#FFFFFF" />
    </View>
  );
}
