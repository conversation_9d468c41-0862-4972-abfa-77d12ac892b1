import { router } from 'expo-router';
import React from 'react';
import { Image, ImageBackground, StatusBar, Text, View } from 'react-native';

import ButtonWithIcon from '@/components/button-with-icon';
import { Button } from '@/components/ui';

export default function Login() {
  return (
    <View className="relative w-full flex-1">
      <StatusBar barStyle={'light-content'} />
      <ImageBackground
        source={require('../../../assets/images/signup.png')}
        resizeMode="cover"
        style={{ height: '100%', width: '100%' }}
      />
      {/* Section 1 back-icon */}

      <View className="absolute size-full px-5">
        {/* <BackButton text="Back" onPress={() => router.back()} /> */}

        {/* Section 2 */}
        <View className="flex-1 justify-center gap-[70px]">
          <View className="items-center gap-6">
            <Image
              source={require('../../../assets/images/logo.png')}
              resizeMode="contain"
              style={{ height: 109, width: 162 }}
            />
            <Text className="font-PoppinsBold text-[20px]  font-bold leading-[30px] text-black-0">
              Explore the World with Confidence
            </Text>
          </View>
          {/* Section3 */}

          <View className=" w-full  gap-10">
            <Button
              onPress={() => {
                router.push('/auth/create-account');
                console.log('hiii');
              }}
              label="Sign up free"
              variant="secondary"
              className=" bg-black-0 "
              textClassName="font-inter text-[16px] font-medium leading-[24px] dark:text-blue"
            />
            <View className="w-full flex-row items-center gap-2   ">
              <View className="flex-1 border border-secondary-0"></View>
              <Text className="text-center font-inter text-[16px] font-normal leading-5 text-black-50">
                or continue with
              </Text>
              <View className="flex-1 border border-secondary-0"></View>
            </View>

            <ButtonWithIcon
              onPress={() => {
                router.push('/auth/login-guest');
              }}
            />
          </View>
        </View>
      </View>
    </View>
  );
}
