import { Image } from 'expo-image';
import { router } from 'expo-router';
import { useState } from 'react';
import React from 'react';
import { Modal, Pressable, ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import CompanionDetailCard from '@/components/companion-detailCard';
import FlightInfoCard from '@/components/flight-info-card';
import Reviews from '@/components/reviews';

interface OptionsProps {
  options: string[];
  selectedOption: string | null;
  setSelectedOption: React.Dispatch<React.SetStateAction<string | null>>;
}

const Options: React.FC<OptionsProps> = ({
  options,
  selectedOption,
  setSelectedOption,
}) => {
  return (
    <View className="flex-row flex-wrap items-center">
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => setSelectedOption(option)}
          className={`mb-2.5 mr-4 rounded-[10px] px-5 py-3  ${
            selectedOption === option
              ? 'dark:bg-primary-50'
              : 'dark:bg-primary-900'
          }`}
        >
          <Text
            className={`className="font-inter text-[14px] font-normal leading-5  ${
              selectedOption === option ? 'dark:text-blue' : 'dark:text-black-0'
            }`}
          >
            {option}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

export default function AvailableCompanion() {
  const [openModal, setOpenModal] = useState(false);

  const [traveler, setTraveler] = useState<string | null>(null);
  const [gender, setGender] = useState<string | null>(null);
  const [priceRange, setPriceRange] = useState<string | null>(null);
  const [checked, setChecked] = useState<string>('first');

  const clearFilters = () => {
    setTraveler(null);
    setGender(null);
    setPriceRange(null);
    setChecked('first');
  };

  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="pt-safe flex-1 items-center justify-end  dark:bg-bgmodal">
          <View className="w-full rounded-t-lg bg-white p-5 dark:bg-primary-950">
            <View className="mb-4 flex-row items-center justify-between">
              <Text className="font-PoppinsSemiBold text-[20px] font-semibold leading-[30px] dark:text-black-50">
                Filter & Sorting
              </Text>
              <Pressable onPress={() => setOpenModal(false)}>
                <Image
                  source={require('../../../../assets/images/cross.png')}
                  contentFit="contain"
                  style={{ height: 24, width: 24 }}
                />
              </Pressable>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              <View className="gap-5">
                <View>
                  <Text className="mb-2.5 font-PoppinsSemiBold text-base font-semibold leading-[24px] dark:text-black-50">
                    Traveler
                  </Text>
                  <Options
                    options={['Solo', 'Group', 'Family']}
                    selectedOption={traveler}
                    setSelectedOption={setTraveler}
                  />
                </View>

                <View>
                  <Text className="mb-2.5 font-PoppinsSemiBold text-base font-semibold leading-[24px] dark:text-black-0">
                    Gender Preference
                  </Text>
                  <Options
                    options={['Female', 'Male', 'Transgender', 'LGBTQ++']}
                    selectedOption={gender}
                    setSelectedOption={setGender}
                  />
                </View>

                <View>
                  <Text className="mb-2.5 font-PoppinsSemiBold text-base font-semibold leading-[24px] dark:text-black-0">
                    Price Range
                  </Text>
                  <Options
                    options={[
                      '$50- $60',
                      '$70-$100',
                      '$100-$200',
                      '$200-$500++',
                    ]}
                    selectedOption={priceRange}
                    setSelectedOption={setPriceRange}
                  />
                </View>

                <View>
                  <Text className="font-inter text-base font-semibold leading-[24px] dark:text-black-0">
                    Reviews
                  </Text>
                  <View>
                    <Reviews checked={checked} setChecked={setChecked} />
                  </View>
                </View>

                <View className="mb-8 w-full flex-row justify-between">
                  <Pressable
                    className="flex-1 items-center rounded-xl px-6 py-3.5 dark:bg-primary-950"
                    onPress={clearFilters}
                  >
                    <Text className="text-medium text-center font-inter text-sm font-medium leading-5 text-black-0 dark:text-black-50">
                      Clear Result
                    </Text>
                  </Pressable>

                  <Pressable
                    className="flex-1 items-center rounded-xl border border-black-0 px-6 py-3.5 dark:bg-primary-50"
                    onPress={() => {
                      setOpenModal(false);
                    }}
                  >
                    <Text className="text-medium text-center font-inter text-sm font-medium leading-5 text-black-0 dark:text-blue">
                      Apply Filter
                    </Text>
                  </Pressable>
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <View className="flex-row items-center justify-between">
          <Pressable
            className="flex-row items-center gap-2"
            onPress={() => router.back()}
          >
            <Image
              source={require('../../../../assets/images/back-icon.svg')}
              contentFit="contain"
              style={{ height: 10, width: 8 }}
              tintColor={'white'}
            />

            <Text className="font-inter text-[14px] font-medium leading-5 text-black-0">
              Back
            </Text>
          </Pressable>

          <Text className="font-PoppinsBold text-xl font-bold leading-[30px] text-black-0 dark:text-black-100">
            Available Companion
          </Text>
          <Pressable onPress={() => setOpenModal(!openModal)}>
            <Image
              source={require('../../../../assets/images/filter.png')}
              contentFit="contain"
              style={{ height: 24, width: 24 }}
              tintColor={'white'}
            />
          </Pressable>
        </View>
        <View className="flex-1 gap-4">
          <View className="mt-4">
            <FlightInfoCard />
          </View>
          <ScrollView
            className="mb-5 flex-1"
            showsVerticalScrollIndicator={false}
          >
            <Pressable
              onPress={() =>
                router.push('/form/companion-required/profilepage-companion')
              }
            >
              <CompanionDetailCard
                variant="default"
                title="Kaushiki"
                subTitle="Female, 25yrs, Indian"
                rating=""
              />
            </Pressable>
            <Pressable
              onPress={() =>
                router.push('/form/companion-required/profilepage-companion')
              }
            >
              <CompanionDetailCard
                variant="secondary"
                title="Shivangi"
                subTitle="Female, 5yrs, Indian"
                rating=""
              />
            </Pressable>
            <Pressable
              onPress={() =>
                router.push('/form/companion-required/profilepage-companion')
              }
            >
              <CompanionDetailCard
                variant="default"
                title="Gargi"
                subTitle="Female, 25yrs, Indian"
                rating=""
              />
            </Pressable>
            <Pressable
              onPress={() =>
                router.push('/form/companion-required/profilepage-companion')
              }
            >
              <CompanionDetailCard
                variant="secondary"
                title="Kaushiki"
                subTitle="Female, 35yrs, Indian"
                rating=""
              />
            </Pressable>
            <Pressable
              onPress={() =>
                router.push('/form/companion-required/profilepage-companion')
              }
            >
              <CompanionDetailCard
                variant="default"
                title="Kaushiki"
                subTitle="Female, 35yrs, Indian"
                rating=""
              />
            </Pressable>

            {renderModal()}
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
}
