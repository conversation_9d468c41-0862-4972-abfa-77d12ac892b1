import { Env } from '@env';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import InputText from '@/components/input-txt';
import { Button } from '@/components/ui';
import { databases } from '@/lib/appwrite';
import { useLanguageStore } from '@/store/language-store';

export default function Search() {
  const { addLanguage, removeLanguage, languageThatTravellerSpeaks } =
    useLanguageStore((state) => {
      return {
        addLanguage: state.addLanguage,
        removeLanguage: state.removeLanguage,
        languageThatTravellerSpeaks: state.languageThatTravellerSpeaks,
      };
    });
  const [availableLanguages, setAvailableLanguages] = useState<
    { name: string; id: string }[]
  >([]);

  const fetchLanguages = async () => {
    const info = await databases.listDocuments(
      Env.COLLECTION_ID,
      Env.COLLECTION_LANGUAGES
    );
    const document = info.documents.map((document) => ({
      name: document.name,
      id: document.$id,
    }));
    setAvailableLanguages(document);
  };

  //getting the languages from database
  useEffect(() => {
    fetchLanguages();
  }, []);

  const [showMore, setShowMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const saveLanguages = () => {
    router.back();
    // router.push({
    //   pathname: '/form/companion-required/traveler-details',
    //   params: {
    //     selectedLanguages: JSON.stringify(
    //       languageThatTravellerSpeaks.map((i) => i.id)
    //     ),
    //   },
    // });
  };

  const filteredLanguages = availableLanguages.filter(
    (language) =>
      !languageThatTravellerSpeaks
        .map((spoken) => spoken.id)
        .includes(language.id)
  );

  const toggleShowMore = () => {
    setShowMore(!showMore);
  };

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 text-black-950 dark:bg-primary-950">
        <Pressable
          onPress={() => {
            router.back();
          }}
        >
          <Image
            source={require('../../../../assets/images/cross.png')}
            contentFit="contain"
            style={{ height: 28, width: 28 }}
          />
        </Pressable>

        <View className="mt-5 gap-2">
          <Text className="font-PoppinsBold text-xl font-bold leading-[30px] dark:text-black-50">
            Choose Language Traveler Speaks
          </Text>
          <Text className="font-inter text-[14px] font-medium text-black-950 dark:text-black-300">
            Add the language that traveler speaks, add at least 3 languages that
            you speak.
          </Text>
        </View>

        <View className="mt-6 flex-1 justify-between">
          <View className="gap-8">
            <InputText
              value={searchQuery}
              onChangeText={setSearchQuery}
              onPress={() => {}}
              placeholder="Language you spoke the most?"
              placeholderTextColor={'#B3B3B3'}
              className="border-0 dark:bg-primary-900"
              iconSourceFirst={require('../../../../assets/images/search-icon.png')}
              iconStyleFirst={{ height: 14, width: 14 }}
            />

            <View className="w-full flex-row flex-wrap items-center gap-4 dark:bg-primary-950">
              {languageThatTravellerSpeaks.map((language) => (
                <Pressable
                  onPress={() => removeLanguage(language.id, language.name)}
                  key={language.id}
                >
                  <View
                    className="rounded-[16px] border px-2.5 py-[6px] dark:border-bgtextInput "
                    key={language.id}
                  >
                    <View className="flex-row items-center gap-1">
                      <Text className="font-inter font-semibold leading-5 dark:text-black-100 ">
                        {language.name}
                      </Text>
                      <Image
                        source={require('../../../../assets/images/circlecross.png')}
                        contentFit="contain"
                        style={{ height: 12, width: 12 }}
                      />
                    </View>
                  </View>
                </Pressable>
              ))}
            </View>

            <View className="flex-row flex-wrap items-center gap-4 dark:bg-primary-950">
              {filteredLanguages.map((language) => (
                <Pressable
                  onPress={() => addLanguage(language.id, language.name)}
                  key={language.id}
                >
                  <View
                    className="rounded-[16px] border bg-customborder px-2.5 py-[6px] dark:border-primary-50 "
                    key={language.id}
                  >
                    <View className="flex-row items-center gap-1">
                      <Text className="font-inter font-semibold leading-5 dark:text-primary-50 ">
                        {language.name}
                      </Text>
                      <Image
                        source={require('../../../../assets/images/plus.png')}
                        contentFit="contain"
                        style={{ height: 12, width: 12 }}
                      />
                    </View>
                  </View>
                </Pressable>
              ))}
            </View>
          </View>

          <View>
            <Button
              variant="secondary"
              label="Save"
              className="mb-4"
              onPress={saveLanguages}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
