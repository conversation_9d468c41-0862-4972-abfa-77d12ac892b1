import { Image } from 'expo-image';
import { router } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import React from 'react';
import { FlatList, Pressable, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import InputText from '@/components/input-txt';
import { Button } from '@/components/ui';
import { useAirportStore } from '@/store/airport-store';
import { useBookingStore } from '@/store/booking-store';

export default function DestinationSearch() {
  const { setSelectedAirports } = useBookingStore((state) => {
    return {
      setSelectedAirports: state.setSelectedAirports,
    };
  });
  const { airports, isLoading } = useAirportStore();
  const { index } = useLocalSearchParams();
  console.log('index is', index);

  const handleAirportSelect = (item: Airport) => {
    const newSelectedAirport = {
      index: String(index),
      id: item.id,
      name: item.name,
    };

    setSelectedAirports(newSelectedAirport);
    router.back();
  };

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <View className="flex-row items-center justify-between">
          <Pressable
            onPress={() => {
              router.back();
            }}
          >
            <Image
              source={require('../../../../assets/images/cross.png')}
              contentFit="contain"
              style={{ height: 28, width: 28 }}
            />
          </Pressable>
          <View className="rounded-[12px] bg-black-0  px-[27px] py-3 dark:bg-primary-900 ">
            <Text className="font-inter text-[16px] font-normal text-black-950 dark:text-black-0">
              Save
            </Text>
          </View>
        </View>
        <View className="flex-1 justify-between">
          <View className="mb-3 mt-6 flex-row items-center">
            <InputText
              onPress={() => {}}
              placeholder="IND"
              className="flex-1 dark:bg-blue"
              iconSourceFirst={require('../../../../assets/images/search-icon.png')}
              iconStyleFirst={{ height: 16, width: 16 }}
              iconSourceSecond={require('../../../../assets/images/cross.png')}
              iconStyleSecond={{ height: 16, width: 16 }}
            />
            <Pressable>
              <Text className="dark: ml-2 font-inter font-normal leading-6 text-black-50">
                Cancel
              </Text>
            </Pressable>
          </View>
          {isLoading ? (
            <View className="flex-1 items-center justify-center">
              <Text className="text-white">Loading airports...</Text>
            </View>
          ) : (
            <FlatList
              data={airports}
              renderItem={({ item }) => (
                <Pressable onPress={() => handleAirportSelect(item)}>
                  <Text className="p-3 text-white">{item.name}</Text>
                </Pressable>
              )}
              keyExtractor={(item) => item.id}
            />
          )}
          <View className="mb-4">
            <Button variant="secondary" label="Save" onPress={() => {}} />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
