import { Env } from '@env';
import { zodResolver } from '@hookform/resolvers/zod';
import DateTimePicker, {
  type DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { addDays } from 'date-fns';
import { router } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Alert,
  Image,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputErrorMsg from '@/components/input-error-msg';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button, ControlledSelect } from '@/components/ui';
import AirportSelectionInput from '@/components/ui/airport-selection-input';
import { flightSchema } from '@/form-schema/form-schema';
import { databases } from '@/lib/appwrite';
import { AIRLINES } from '@/store/airport-store';
import { useBookingStore } from '@/store/booking-store';

export default function TravelerFlightDates() {
  const { selectedStep: initialStep, id, onboarding } = useLocalSearchParams();
  const [selectedStep, setSelectedStep] = useState(Number(initialStep) || 0);
  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [swapped, setSwapped] = useState(false);
  const [loading, setLoading] = useState(false);

  // State to store the added interconnected flights
  //   const [flights, setFlights] = useState<Flight[]>([]);

  const handleSwap = () => {
    setSwapped(!swapped);
  };

  const toggleDatepicker = () => {
    setShowDatePicker(true);
  };

  const onDateChange = (
    event: DateTimePickerEvent,
    selectedDate: Date | undefined
  ) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'android' ? false : true);
    setDate(currentDate);

    const formattedDate = currentDate.toDateString();
    setValue('flightDate', formattedDate, { shouldValidate: true });
  };

  const toggleTimepicker = () => {
    setShowTimePicker(true);
  };

  const onTimeChange = (
    event: DateTimePickerEvent,
    selectedTime: Date | undefined
  ) => {
    const currentTime = selectedTime || time;
    setShowTimePicker(Platform.OS === 'android' ? false : true);
    setTime(currentTime);

    const formattedTime = currentTime.toTimeString().split(' ')[0];
    setValue('flightTime', formattedTime, { shouldValidate: true });
  };
  const {
    // countOfInterConnectedAirport,
    // setCountOfInterConnectedAirport,
    addEmptyAirport,
    selectedAirports,
    setFlightBookingid,
    setSelectedAirports,
    removeAirport,
  } = useBookingStore((state) => {
    return {
      selectedAirports: state.selectedAirports.sort(
        (a, b) => Number(a.index) - Number(b.index)
      ),
      setFlightBookingid: state.setFlightBookingid,
      setSelectedAirports: state.setSelectedAirports,
      // setCountOfInterConnectedAirport: state.setCountOfInterConnectedAirport,
      // countOfInterConnectedAirport: state.countOfInterConnectedAirport,
      removeAirport: state.removeAirport,
      addEmptyAirport: state.addEmptyAirport,
    };
  });

  // Function to add a new flight to the list
  const addNewFlight = () => {
    addEmptyAirport();
    // const newCount = countOfInterConnectedAirport + 1;
    // setCountOfInterConnectedAirport();
    // setSelectedAirports({
    //   id: newCount.toString(),
    //   name: '',
    //   index: newCount.toString(),
    // });
  };

  const { control, handleSubmit, setValue } = useForm<
    z.infer<typeof flightSchema>
  >({
    resolver: zodResolver(flightSchema),
    defaultValues: {
      flightCompany: '',
      flightPNR: '',
      flightDate: addDays(new Date(), 1).toDateString(),
      flightTime: new Date().toTimeString().split(' ')[0],
    },
  });

  const onSubmit = handleSubmit(
    async (data) => {
      const allAirportSelected = selectedAirports.reduce(
        (prev, curr) => prev && curr.name !== '',
        true
      );

      if (!allAirportSelected) {
        Alert.alert('Please select all destinations');
        return;
      }
      setLoading(true);
      console.log('submit with data', data);
      const dateObj = new Date(data.flightDate);
      const [hours, mins, sec] = data.flightTime.split(':').map(Number);
      dateObj.setUTCHours(hours, mins, sec);
      const flightDateTime = dateObj.toISOString();

      try {
        let flightBooking;
        try {
          // First try to get the document to check if it exists
          flightBooking = await databases.getDocument(
            Env.COLLECTION_ID,
            Env.COLLECTION_BOOKING_FLIGHT_DETAILS,
            id as string
          );

          // If document exists, update it
          flightBooking = await databases.updateDocument(
            Env.COLLECTION_ID,
            Env.COLLECTION_BOOKING_FLIGHT_DETAILS,
            id as string,
            {
              flightPNR: data.flightPNR,
              flightCompany: data.flightCompany,
              flightTime: flightDateTime,
              bookingDestinations: selectedAirports.map((airport) => ({
                order: Number(airport.index),
                airports: airport.id,
              })),
            }
          );
        } catch (error) {
          // If document doesn't exist, create it
          flightBooking = await databases.createDocument(
            Env.COLLECTION_ID,
            Env.COLLECTION_BOOKING_FLIGHT_DETAILS,
            id as string,
            {
              flightPNR: data.flightPNR,
              flightCompany: data.flightCompany,
              flightTime: flightDateTime,
              bookingDestinations: selectedAirports.map((airport) => ({
                order: Number(airport.index),
                airports: airport.id,
              })),
            }
          );
        }
        setFlightBookingid(flightBooking.$id);
        console.log('flight booking created successfully', flightBooking);
      } catch (err: any) {
        setLoading(false);
        showMessage({
          message: 'Error in creating flight booking',
          type: 'danger',
        });
        console.log('error in flight booking is', err);
      }
      setLoading(false);
      setSelectedStep(selectedStep + 1);
      router.push({
        pathname: '/form/companion-required/photo-uploads',
        params: {
          selectedStep: selectedStep + 1,
          id,
          onboarding: onboarding === 'true' ? 'true' : 'false',
        },
      });
    },
    (errors) => {
      console.log('errors during form submission', errors);
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={selectedStep}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />

        <View className="flex-1 gap-8">
          <View className="mt-4 gap-2">
            <Text className="font-PoppinsBold text-[20px] font-bold leading-[30px] text-black-950 dark:text-black-0">
              Add Flight Details
            </Text>
            <Text className="font-inter text-[14px] font-medium leading-5 text-black-950 dark:text-black-300">
              Add traveler's flight details to connect with companion
            </Text>
          </View>

          <ScrollView
            className="mb-5 w-full flex-1"
            showsVerticalScrollIndicator={false}
          >
            <View className="w-full flex-1 gap-5">
              <View>
                <View className="flex-row items-center justify-between">
                  <Text className=" font-PoppinsMedium text-[16px] font-medium leading-[24px] text-black-950 dark:text-black-50">
                    From & To Destination
                  </Text>
                  {/* plus icon */}
                  <TouchableOpacity onPress={addNewFlight}>
                    <View className="rounded-[8px] border border-customborder bg-black-0 p-[6px] dark:bg-primary-950">
                      <Image
                        source={require('../../../../assets/images/plus.png')}
                        resizeMode="contain"
                        style={{ height: 20, width: 20 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>

                <View className="flex-row items-center gap-2">
                  <View className="mt-2 flex-1 flex-col">
                    {selectedAirports.map((airport, index) => (
                      <AirportSelectionInput
                        airport={airport}
                        index={index}
                        key={index}
                        setAirport={(airport) => {
                          setSelectedAirports({
                            ...airport,
                          });
                        }}
                        removeAirport={removeAirport}
                        totalFlights={selectedAirports.length}
                      />
                    ))}
                    {/* <Pressable
                      onPress={() => {
                        router.push(
                          `/form/companion-required/destination-search?index=${0}`
                        );
                      }}
                    >
                      <View
                        className="mt-2
 flex-row items-center gap-2 rounded-t-[12px] border-x border-t border-borderdark px-5 py-[14px] dark:bg-bgtextInput"
                      >
                        <Image
                          source={require('../../../../assets/images/location.png')}
                          resizeMode="contain"
                          style={{ width: 12, height: 15 }}
                        />
                        <Text
                          className={cn(
                            'text-black-950 leading-5 text-[14px] font-normal dark:text-black-300'
                          )}
                        >
                          {selectedAirports.find(
                            (airport) => airport.index === 0
                          )?.name || (swapped ? 'From where?' : 'Where to?')}
                        </Text>
                      </View>
                    </Pressable> */}
                  </View>

                  {/* <TouchableOpacity onPress={handleSwap}>
                    <View className="rounded-[8px] border border-customborder bg-black-0 p-[6px] dark:bg-primary-950">
                      <Image
                        source={require('../../../../assets/images/swaparrow.png')}
                        resizeMode="contain"
                        style={{ height: 20, width: 20 }}
                      />
                    </View>
                  </TouchableOpacity> */}
                </View>
              </View>

              <InputLabelled label="Airline Company">
                <Controller
                  name="flightCompany"
                  control={control}
                  render={({ field: { value, onChange }, fieldState }) => (
                    <View className="mt-2">
                      <ControlledSelect
                        name="flightCompany"
                        control={control}
                        options={AIRLINES.map((airline) => ({
                          label: airline.name,
                          value: airline.name,
                        }))}
                        placeholder="Select Airline"
                        value={value ?? ''}
                        onSelect={onChange}
                      />
                      {/* <InputText
                        placeholder="Airline"
                        value={value}
                        onChangeText={onChange}
                        iconSourceFirst={require('../../../../assets/images/plane.png')}
                        iconStyleFirst={{ height: 15, width: 15 }}
                      /> */}
                      {/* {fieldState.error?.message && (
                        <InputErrorMsg message={fieldState.error.message} />
                      )} */}
                    </View>
                  )}
                />
              </InputLabelled>

              <InputLabelled label="Flight PNR/Ref/Confirmation Number">
                <Controller
                  name="flightPNR"
                  control={control}
                  render={({ field: { value, onChange }, fieldState }) => (
                    <>
                      <InputText
                        placeholder="89CBSK6"
                        value={value}
                        onChangeText={(text) => {
                          //replace all the characters except 0-9 with ''
                          // const numericText = text.replace(/[^0-9]/g, '');
                          onChange(text);
                        }}
                        iconSourceFirst={require('../../../../assets/images/user-text-input.svg')}
                        iconStyleFirst={{ height: 16, width: 13 }}
                      />
                      {fieldState.error?.message && (
                        <InputErrorMsg message={fieldState.error.message} />
                      )}
                    </>
                  )}
                />
              </InputLabelled>

              <InputLabelled label="Flight Date">
                {Platform.OS === 'ios' ? (
                  <Controller
                    name="flightDate"
                    control={control}
                    render={({ field: { value, onChange }, fieldState }) => (
                      <>
                        <Pressable
                          onPress={() => setShowDatePicker(true)}
                          className="mt-2 items-start rounded-xl border border-customborder dark:bg-bgtextInput "
                        >
                          <DateTimePicker
                            value={date}
                            mode="date"
                            display="default"
                            minimumDate={addDays(new Date(), 1)}
                            onChange={onDateChange}
                          />
                        </Pressable>
                        {fieldState?.error?.message && (
                          <InputErrorMsg message={fieldState?.error?.message} />
                        )}
                      </>
                    )}
                  />
                ) : Platform.OS === 'android' ? (
                  <Controller
                    name="flightDate"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <InputText
                          editable={false}
                          onPress={toggleDatepicker}
                          placeholder="Select Date"
                          value={field.value}
                          onChangeText={(num) => {
                            field.onChange(num);
                          }}
                          iconSourceFirst={require('../../../../assets/images/calender.png')}
                          iconStyleFirst={{ height: 15, width: 15 }}
                        />
                        {fieldState.error?.message && (
                          <InputErrorMsg
                            message={fieldState.error.message}
                          ></InputErrorMsg>
                        )}
                        {showDatePicker && (
                          <DateTimePicker
                            value={date}
                            mode="date"
                            display="default"
                            onChange={onDateChange}
                          />
                        )}
                      </>
                    )}
                  />
                ) : null}
              </InputLabelled>

              <InputLabelled label="Flight Time">
                {Platform.OS === 'ios' ? (
                  <Controller
                    name="flightTime"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <Pressable
                          onPress={() => setShowTimePicker(true)}
                          className="mt-2 items-start rounded-xl border border-customborder dark:bg-bgtextInput "
                        >
                          <DateTimePicker
                            value={time}
                            mode="time"
                            display="default"
                            onChange={onTimeChange}
                          />
                        </Pressable>
                        {fieldState?.error?.message && (
                          <InputErrorMsg message={fieldState?.error?.message} />
                        )}
                      </>
                    )}
                  />
                ) : Platform.OS === 'android' ? (
                  <Controller
                    name="flightTime"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <InputText
                          editable={false}
                          onPress={toggleTimepicker}
                          value={field.value}
                          onChangeText={(value) => {
                            field.onChange(value);
                          }}
                          placeholder="Select Time"
                          iconSourceFirst={require('../../../../assets/images/watch.png')}
                          iconStyleFirst={{ height: 15, width: 15 }}
                        />
                        {fieldState.error?.message && (
                          <InputErrorMsg
                            message={fieldState.error.message}
                          ></InputErrorMsg>
                        )}
                        {showTimePicker && (
                          <DateTimePicker
                            value={time}
                            mode="time"
                            display="default"
                            onChange={onTimeChange}
                          />
                        )}
                      </>
                    )}
                  />
                ) : null}
              </InputLabelled>
            </View>
          </ScrollView>
        </View>

        <View className="mb-5 flex-row items-center justify-between px-5">
          <Pressable onPress={() => router.back()}>
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-950">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-black-50">
                Back
              </Text>
            </View>
          </Pressable>
          <Button
            variant="secondary"
            label="Next"
            className="w-40"
            loading={loading}
            onPress={onSubmit}
          />
          {/* <Pressable onPress={onSubmit}>
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-50">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-blue">
                Next
              </Text>
            </View>
          </Pressable> */}
        </View>
      </View>
    </SafeAreaView>
  );
}
