/* eslint-disable max-lines-per-function */
import { Env } from '@env';
import { zodResolver } from '@hookform/resolvers/zod';
import { Image } from 'expo-image';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Pressable, Text, View } from 'react-native';
import { ID } from 'react-native-appwrite';
import { showMessage } from 'react-native-flash-message';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button, Radio, Switch } from '@/components/ui';
import GenderSelector from '@/components/ui/gender-selector';
import LanguagesSelector from '@/components/ui/languages-selector';
import { userSchema } from '@/form-schema/form-schema';
import { account, databases } from '@/lib/appwrite';
import { cn } from '@/lib/utils';
import { useBookingStore } from '@/store/booking-store';

export default function TravelerDetails() {
  const [selectedStep, setSelectedStep] = useState(0);
  const { onboarding } = useLocalSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: '',
      // phoneNo: '',
      // email: '',
      about: '',
      typeOfTraveller: 'SOLO',
      genderPreference: 'MALE',
      openToAllGenders: false,
      languages: [],
      bookingFor: 'Yourself',
    },
  });

  const bookingFor = watch('bookingFor');
  const openToAllGenders = watch('openToAllGenders');

  const getDetails = async () => {
    const user = await account.get();

    if (onboarding === 'true') {
      console.log('onboarding is true');
      const user = await account.get();
      console.log('user onboarding', user);
      const travellerDetails = await databases.getDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_BOOKING_TRAVELLERS,
        user.$id
      );
      console.log('traveller details onboarding', user, travellerDetails);
      reset({
        name: user.name,
        // email: user.email,
        // phoneNo: user.phone,
        about: travellerDetails.about,
        typeOfTraveller: travellerDetails.typeOfTraveller,
        openToAllGenders: travellerDetails.openToAllGenders,
        genderPreference: travellerDetails.genderPreference,
        gender: travellerDetails.gender,
        bookingFor: travellerDetails.bookingFor,
        languages: travellerDetails.languages.map((i) => i.$id),
      });
      return;
    }

    // if (bookingTravellerId) {
    //   const travellerDetails = await databases.getDocument(
    //     Env.COLLECTION_ID,
    //     Env.COLLECTION_BOOKING_TRAVELLERS,
    //     bookingTravellerId
    //   );
    //   console.log('traveller details for someone else', user, travellerDetails);
    //   reset({
    //     name: user.name,
    //     // email: user.email,
    //     // phoneNo: user.phone,
    //     about: travellerDetails.about,
    //     typeOfTraveller: travellerDetails.typeOfTraveller,
    //     openToAllGenders: travellerDetails.openToAllGenders,
    //     genderPreference: travellerDetails.genderPreference,
    //     gender: travellerDetails.gender,
    //     bookingFor: travellerDetails.bookingFor,
    //   });
    // } else {
    reset({
      name: user.name,
      // email: user.email,
      // phoneNo: user.phone,
    });
    // }
  };

  const getTravellerDetails = async () => {
    if (bookingFor === 'Yourself') {
      const user = await account.get();
      setValue('name', user.name);
    } else {
      setValue('name', '');
    }
  };

  // initial
  useEffect(() => {
    console.log('traveller details fetching');
    getDetails();
  }, []);

  //if the booking is for someone else
  // useEffect(() => {
  //   console.log('traveller details bookingTravellerId');
  //   getDetails();
  // }, [bookingTravellerId]);

  //if the booking is for yourself
  useEffect(() => {
    console.log('traveller details getTravellerDetails bookingFor');
    getTravellerDetails();
  }, [bookingFor]);

  //form submission
  const onSubmit = handleSubmit(
    async (data: z.infer<typeof userSchema>) => {
      setIsLoading(true);

      const user = await account.get();

      const documentData = {
        name: data.name,
        bookingFor: data.bookingFor,
        gender: data.gender,
        languages: data.languages || [],
        genderPreference: data.genderPreference,
        openToAllGenders: data.openToAllGenders,
        about: data.about,
        typeOfTraveller: data.typeOfTraveller,
        // typeOfUser: 'TRAVELLER',
      };
      // if (documentData.bookingFor === 'Yourself') {
      //   try {
      //     const updatedDocument = await databases.updateDocument(
      //       '6711d07f002f03a35d08',
      //       '675aaac0000029cc416c',
      //       ID.unique(),
      //       documentData
      //     );
      //     console.log('document updated successfully', updatedDocument);
      //     setBookingTravellerId(updatedDocument.$id);
      //   } catch (updateError: any) {
      //     console.log('Error in updating the document', updateError);
      //   }
      // } else {
      const id = onboarding === 'true' ? user.$id : ID.unique();

      console.log('id', id);
      let travelerDocument;

      try {
        console.log('getting traveler document');
        // First try to get the document to check if it exists
        travelerDocument = await databases.getDocument(
          Env.COLLECTION_ID,
          Env.COLLECTION_BOOKING_TRAVELLERS,
          id
        );

        console.log('updating traveler document', travelerDocument);
        // If document exists, update it
        travelerDocument = await databases.updateDocument(
          Env.COLLECTION_ID,
          Env.COLLECTION_BOOKING_TRAVELLERS,
          id,
          {
            UID: user.$id,
            ...documentData,
          }
        );
        console.log('Traveler document updated successfully', travelerDocument);
      } catch (error) {
        console.log(
          'error in updating traveler document so creating it',
          error
        );
        try {
          // If document doesn't exist, create it
          travelerDocument = await databases.createDocument(
            Env.COLLECTION_ID,
            Env.COLLECTION_BOOKING_TRAVELLERS,
            id,
            {
              UID: user.$id,
              ...documentData,
            }
          );
          console.log(
            'Traveler document created successfully',
            travelerDocument
          );
        } catch (error) {
          console.log('error in creating traveler document', error);
          showMessage({
            message: 'Error in creating traveler document',
            type: 'danger',
          });
          setIsLoading(false);
          return;
        }
      }

      router.push({
        pathname: '/form/companion-required/flight-dates',
        params: {
          selectedStep: selectedStep + 1,
          id,
          onboarding: onboarding === 'true' ? 'true' : 'false',
        },
      });
      setIsLoading(false);
    },
    (errors) => {
      console.error('Form validation errors:', errors);
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={selectedStep}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />
        <View className="flex-1 gap-8">
          <View className="mt-4 gap-2">
            <Text className=" font-PoppinsBold text-[20px] font-bold leading-[30px] dark:text-black-50">
              Add Travellers Details
            </Text>
            <Text className="dark: font-inter text-sm font-medium leading-5 text-black-300">
              Add travellers details to connect with companion
            </Text>
          </View>

          <KeyboardAwareScrollView
            className="flex-1"
            showsVerticalScrollIndicator={false}
          >
            <View className="flex-1 gap-[18px] ">
              <InputLabelled label="Are you Booking for? *">
                <View className="mt-2.5 rounded-[12px] border border-borderdark px-5 py-[14px]  dark:bg-bgtextInput">
                  <Controller
                    control={control}
                    name="bookingFor"
                    rules={{
                      validate: (value) => {
                        if (value != 'Father' || 'Mother' || 'Relative') {
                          return 'Please select one of the options.';
                        }
                        return true;
                      },
                    }}
                    render={({ field: { onChange, value } }) => (
                      <>
                        <View className="flex-row justify-between ">
                          <Pressable
                            onPress={() => {
                              onChange('Yourself');
                            }}
                          >
                            <View
                              className={cn(
                                'px-3 py-2',
                                value === 'Yourself'
                                  ? 'bg-stromegray rounded-xl border border-borderdark'
                                  : null
                              )}
                            >
                              <Text
                                className={cn(
                                  'text-inter leading-5 text-[14px] font-normal ',
                                  value === 'Yourself'
                                    ? 'dark:text-black-0'
                                    : 'text-black-300'
                                )}
                              >
                                Yourself
                              </Text>
                            </View>
                          </Pressable>

                          <Pressable
                            onPress={() => {
                              onChange('Father');
                            }}
                          >
                            <View
                              className={cn('px-3 py-2', {
                                'bg-stromegray rounded-xl border border-borderdark':
                                  value === 'Father' ||
                                  value === 'Mother' ||
                                  value === 'Relative',
                              })}
                            >
                              <Text
                                className={cn(
                                  'text-inter leading-5 text-[14px] font-normal ',
                                  value === 'Father' ||
                                    value === 'Mother' ||
                                    value === 'Relative'
                                    ? 'dark:text-black-0'
                                    : 'text-black-300'
                                )}
                              >
                                Someone else
                              </Text>
                            </View>
                          </Pressable>
                        </View>
                        {/* options for someone else */}
                        {(value === 'Father' ||
                          value === 'Mother' ||
                          value === 'Relative') && (
                          <View className="mt-2 flex-row justify-between">
                            <View
                              className={cn('px-3 py-2', {
                                'rounded-[10px] dark:bg-primary-900':
                                  value === 'Father',
                              })}
                            >
                              <Radio
                                accessibilityLabel=""
                                label="Father"
                                checked={value === 'Father'}
                                onChange={() => onChange('Father')}
                              />
                            </View>

                            <View
                              className={cn('px-3 py-2', {
                                'rounded-[10px] dark:bg-primary-900':
                                  value === 'Mother',
                              })}
                            >
                              <Radio
                                accessibilityLabel=""
                                label="Mother"
                                checked={value === 'Mother'}
                                onChange={() => onChange('Mother')}
                              />
                            </View>

                            <View
                              className={cn('px-3 py-2', {
                                'rounded-[10px] dark:bg-primary-900':
                                  value === 'Relative',
                              })}
                            >
                              <Radio
                                accessibilityLabel=""
                                label="Relative"
                                checked={value === 'Relative'}
                                onChange={() => onChange('Relative')}
                              />
                            </View>
                          </View>
                        )}
                      </>
                    )}
                  />
                  {errors.bookingFor && (
                    <Text className="text-red-500">
                      {errors.bookingFor.message?.toString()}
                    </Text>
                  )}
                </View>
              </InputLabelled>

              <InputLabelled label="Name of the Traveller">
                <Controller
                  control={control}
                  name="name"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder=""
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
              </InputLabelled>

              <InputLabelled label="Are you a ______ Traveler?">
                <Controller
                  control={control}
                  name="typeOfTraveller"
                  render={({ field: { onChange, value } }) => (
                    <View className="mt-2.5 flex-row justify-between rounded-[12px] border border-borderdark bg-black-0 px-5 py-[14px] text-black-0 dark:bg-bgtextInput">
                      <Pressable
                        onPress={() => {
                          onChange('SOLO');
                        }}
                      >
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              ' bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
                                value === 'SOLO',
                            }
                          )}
                        >
                          <Image
                            source={require('../../../../assets/images/plane.png')}
                            contentFit="contain"
                            style={{ height: 12, width: 12 }}
                          />
                          <Text
                            className={cn(
                              'text-black-0 dark:text-black-300',
                              value === 'SOLO'
                                ? 'dark:text-black-200 '
                                : 'text-black-300'
                            )}
                          >
                            Solo
                          </Text>
                        </View>
                      </Pressable>

                      <Pressable
                        onPress={() => {
                          onChange('FAMILY');
                        }}
                      >
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              ' bg-black-0 dark:bg-primary-900 rounded-[12px]':
                                value === 'FAMILY',
                            }
                          )}
                        >
                          <Image
                            // source={require('../../../../assets/images/plane.png')}
                            source={require('../../../../assets/images/plane.png')}
                            contentFit="contain"
                            style={{ height: 12, width: 12 }}
                          />
                          <Text
                            className={cn(
                              'text-black-0 dark:text-black-300',
                              value === 'FAMILY'
                                ? 'dark:text-black-200'
                                : 'text-black-300'
                            )}
                          >
                            Family
                          </Text>
                        </View>
                      </Pressable>

                      <Pressable
                        onPress={() => {
                          onChange('GROUP');
                        }}
                      >
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              'bg-blue dark:bg-primary-900 rounded-[12px] text-black-0':
                                value === 'GROUP',
                            }
                          )}
                        >
                          <Image
                            source={require('../../../../assets/images/plane.png')}
                            contentFit="contain"
                            style={{ height: 12, width: 12 }}
                          />
                          <Text
                            className={cn(
                              'dark : text-black-300',
                              value === 'GROUP'
                                ? 'dark:text-black-200'
                                : 'text-black-300'
                            )}
                          >
                            Group
                          </Text>
                        </View>
                      </Pressable>
                    </View>
                  )}
                />
              </InputLabelled>
              {errors.typeOfTraveller && (
                <Text className="text-red-500">
                  {errors.typeOfTraveller.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Your Gender?">
                <Controller
                  control={control}
                  name="gender"
                  render={({ field: { value, onChange } }) => (
                    <GenderSelector value={value} onChange={onChange} />
                  )}
                />
              </InputLabelled>
              {errors.gender && (
                <Text className="text-red-500">
                  {errors.gender.message?.toString()}
                </Text>
              )}

              <View>
                <View className="flex-row items-center justify-between">
                  <InputLabelled
                    label="Gender Preference"
                    className="flex-row items-center gap-4"
                  >
                    <Controller
                      control={control}
                      name="openToAllGenders"
                      render={({ field: { onChange, value } }) => (
                        <Switch
                          accessibilityLabel=""
                          label="Open for all genders"
                          className="text-black-300"
                          checked={value}
                          onChange={(checked) => {
                            onChange(checked);
                          }}
                        />
                      )}
                    />
                  </InputLabelled>
                </View>
                {!openToAllGenders && (
                  <Controller
                    control={control}
                    name="genderPreference"
                    render={({ field: { onChange, value } }) => (
                      <GenderSelector
                        value={value ?? 'MALE'}
                        onChange={onChange}
                      />
                    )}
                  />
                )}
                {errors.genderPreference && (
                  <Text className="text-red-500">
                    {errors.genderPreference.message?.toString()}
                  </Text>
                )}
              </View>

              <InputLabelled label="Language Traveler Speaks">
                <Controller
                  control={control}
                  name="languages"
                  render={({ field: { onChange, value } }) => (
                    <LanguagesSelector
                      languageIds={value ?? []}
                      setLanguageIds={onChange}
                    />
                  )}
                />
              </InputLabelled>
              {errors.languages && (
                <Text className="text-red-500">
                  {errors.languages.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Write about you">
                <Controller
                  control={control}
                  name="about"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder="Write about yourself"
                      value={value}
                      onChangeText={onChange}
                      numberOfLines={5}
                      multiline={true}
                      className="h-24"
                      textAlignVertical="top"
                    />
                  )}
                />
              </InputLabelled>

              {errors.about && (
                <Text className="text-red-500">
                  {errors.about.message?.toString()}
                </Text>
              )}
            </View>
          </KeyboardAwareScrollView>
        </View>

        <Button
          variant="secondary"
          label="Next"
          className="mb-2 mt-8"
          loading={isLoading}
          onPress={onSubmit}
        />
      </View>
    </SafeAreaView>
  );
}
