import { Env } from '@env';
import { type BottomSheetModal } from '@gorhom/bottom-sheet';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import React, { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Dimensions, Pressable, ScrollView, Text, View } from 'react-native';
import { ID } from 'react-native-appwrite';
import { showMessage } from 'react-native-flash-message';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button } from '@/components/ui';
import { type bookingSchema } from '@/form-schema/form-schema';
import { databases, storage } from '@/lib/appwrite';
import { tryCatch } from '@/lib/try-catch';
import { updateUserData } from '@/lib/user-data-service';

const { height, width } = Dimensions.get('screen');

export default function PhotoUploads() {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { selectedStep: initialStep, id, onboarding } = useLocalSearchParams();

  const [selectedStep, setSelectedStep] = useState(Number(initialStep) || 0);

  // const [calendarPermission, setCalendarPermission] = useState<boolean>(false);
  // const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
  // const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [loadingPassportImage, setLoadingPassportImage] = useState(false);

  // Request Calendar Permissions
  // useEffect(() => {
  //   const requestPermissions = async () => {
  //     const { status } = await Calendar.requestCalendarPermissionsAsync();
  //     if (status === 'granted') {
  //       setCalendarPermission(true);
  //     } else {
  //       console.log('Calendar permission not granted');
  //     }
  //   };
  //   requestPermissions();
  // }, []);
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    handleSubmit,
  } = useForm<z.infer<typeof bookingSchema>>({
    defaultValues: {
      travellersPhoto: '',
      passportPhoto: '',
      companionCompensation: '',
    },
  });

  // Handle Next Step Button
  // const handleNextStep = () => {
  //   setSelectedStep(selectedStep + 1);
  //   router.push({
  //     pathname: '/form/companion-required/available-companion',
  //     params: { selectedStep: selectedStep + 1 },
  //   });
  // };
  // const snapPoints = ['50%'];
  // const { bookingTravellerId, flightBookingId } = useBookingStore((state) => {
  //   return {
  //     bookingTravellerId: state.bookingTravellerId,
  //     flightBookingId: state.flightBookingId,
  //   };
  // });

  const onSubmit = handleSubmit(
    async (data) => {
      console.log('data is', data, id);
      try {
        const booking = await databases.updateDocument(
          Env.COLLECTION_ID,
          Env.COLLECTION_BOOKING_FLIGHT_DETAILS,
          id as string,
          {
            companionCompensation: Math.round(
              parseFloat(data.companionCompensation) * 100
            ),
            passportPhoto: data.passportPhoto,
            travellersPhoto: data.travellersPhoto,
            status: 'ACTIVE',
          }
        );
        console.log('onboarding is', onboarding);
        if (onboarding === 'true') {
          await updateUserData({
            travellerProfile: true,
          });

          // return;
        }
        router.dismissTo('/');
        showMessage({
          message: 'Booking created successfully',
          type: 'success',
        });
        console.log('booking created is', booking);
      } catch (err) {
        console.log('error in creating booking is', err);
        showMessage({
          message: 'Error in creating booking',
          type: 'danger',
        });
      }
      // bottomSheetModalRef.current?.present();
    },
    (errors) => {
      console.log('errors is', errors);
      showMessage({
        message: 'Please fill all the fields',
        type: 'danger',
      });
    }
  );

  // const handleCloseModal = () => {
  //   bottomSheetModalRef.current?.close();
  // };

  // const showDatePicker = () => {
  //   setIsDatePickerVisible(true);
  // };

  // const hideDatePicker = () => {
  //   setIsDatePickerVisible(false);
  // };

  // const handleDateConfirm = (event: any, date: Date | undefined) => {
  //   if (date) {
  //     setSelectedDate(date);
  //   }
  //   hideDatePicker();
  // };

  const ImagePick = ({
    image,
    pickImage,
    handleRemoveImage,
  }: {
    image: string | null;
    pickImage: () => void;
    handleRemoveImage: () => void;
  }) => {
    return (
      <View className="flex-row items-center">
        {!image && (
          <Pressable
            onPress={pickImage}
            className="mr-8 flex-row items-center justify-center rounded-xl border-2 border-borderdark"
            style={{ height: height * 0.085, width: width * 0.1836 }}
          >
            <Text className="text-xl text-primary-200">+</Text>
            <Image
              source={require('../../../../assets/images/Profilee.png')}
              contentFit="contain"
              className="size-6 p-1"
            />
          </Pressable>
        )}

        {image && (
          <View className="flex-row items-center">
            <Image
              source={{ uri: image }}
              style={{ height: 80, width: 80, borderRadius: 12 }}
            />
            <Pressable
              onPress={handleRemoveImage}
              className="absolute right-0 top-0 rounded-full bg-white p-2"
            >
              <Image
                source={require('../../../../assets/images/delete.png')}
                className="size-4"
              />
            </Pressable>
          </View>
        )}
      </View>
    );
  };

  const travellerImagePick = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images, // Restrict to images only
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    console.log('result for traveller image is', result);
    if (result.canceled) {
      return;
    }

    const image = result.assets[0];
    try {
      const travellerFile = {
        name: `traveller-${id}.jpg`,
        type: image.mimeType ?? 'image/jpeg',
        size: image.fileSize ?? 0,
        uri: image.uri,
      };
      const file = await storage.createFile(
        Env.STORAGE_BUCKET_ID,
        ID.unique(),
        travellerFile
      );
      const url = storage.getFileView(Env.STORAGE_BUCKET_ID, file.$id);
      const data = getValues();
      if (data.travellersPhoto) {
        const results = data.travellersPhoto.split('/');
        const deleteFileId =
          data.travellersPhoto.split('/')[results.length - 2];
        tryCatch(storage.deleteFile(Env.STORAGE_BUCKET_ID, deleteFileId));
      }
      setValue('travellersPhoto', url.href);
    } catch (err) {
      console.log('error in uploading traveller image', err);
      showMessage({
        message: 'Error in uploading traveller image',
        type: 'danger',
      });
    }
  };

  const passportImagePick = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images, // Restrict to images only
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    console.log('result for passport image is', result);
    if (result.canceled) {
      return;
    }

    const image = result.assets[0];
    setLoadingPassportImage(true);
    try {
      const userFile = {
        name: `user-${id}.jpg`,
        type: image.mimeType ?? 'image/jpeg',
        size: image.fileSize ?? 0,
        uri: image.uri,
      };
      console.log('user file is', userFile);
      const file = await storage.createFile(
        Env.STORAGE_BUCKET_ID,
        ID.unique(),
        userFile
      );
      const url = storage.getFileView(Env.STORAGE_BUCKET_ID, file.$id);
      const data = getValues();
      if (data.passportPhoto) {
        const results = data.passportPhoto.split('/');
        const deleteFileId = data.passportPhoto.split('/')[results.length - 2];
        tryCatch(storage.deleteFile(Env.STORAGE_BUCKET_ID, deleteFileId));
      }
      setValue('passportPhoto', url.href);
    } catch (err) {
      console.log('error in uploading passport image', err);
      showMessage({
        message: 'Error in uploading passport image',
        type: 'danger',
      });
    } finally {
      setLoadingPassportImage(false);
    }
  };

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={selectedStep}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />

        {/* Bottom Sheet Modal */}
        {/* <Modal
          ref={bottomSheetModalRef}
          index={0}
          snapPoints={snapPoints}
          backgroundStyle={{ backgroundColor: '#020717' }}
          handleIndicatorStyle={{
            display: 'none',
          }}
        >
          <View className="flex-1 justify-between px-5 py-8">
            <View>
              <Pressable onPress={handleCloseModal} className="mb-6">
                <Image
                  source={require('../../../../assets/images/cancel.png')}
                  className="size-6"
                />
              </Pressable>

              <View className="mb-6 flex-row items-center justify-between">
                <View className="flex-row items-center self-start rounded-xl bg-green px-2 py-1.5">
                  <Image
                    source={require('../../../../assets/images/ellipse.png')}
                    className="mr-1 size-2"
                  />
                  <Text className="font-inter text-sm font-medium text-black-50">
                    1 companion
                  </Text>
                </View>

                <View className="flex-row items-center self-start rounded-xl  bg-green px-2 py-1.5">
                  <Image
                    source={require('../../../../assets/images/ellipse.png')}
                    className="mr-1 size-2"
                    tintColor={'#1F7A5F'}
                  />
                  <Text className="font-inter text-sm font-medium text-black-50">
                    2 companion
                  </Text>
                </View>

                <View className="flex-row items-center self-start rounded-xl bg-green px-2 py-1.5">
                  <Image
                    source={require('../../../../assets/images/ellipse.png')}
                    className="mr-1 size-2"
                    tintColor={'#051410'}
                  />
                  <Text className="font-inter text-sm font-medium text-black-50">
                    {'>'}3 companion
                  </Text>
                </View>
              </View> */}

        {/* Show Calendar Date Picker */}
        {/* {calendarPermission ? (
                <View className="items-center justify-center">
                  {isDatePickerVisible && (
                    <DateTimePicker
                      value={selectedDate || new Date()}
                      mode="date"
                      display="default"
                      onChange={handleDateConfirm}
                    />
                  )}
                  {selectedDate && (
                    <Text className="mt-5 text-black-500">
                      Selected Date: {selectedDate.toLocaleDateString()}
                    </Text>
                  )}
                  <Button
                    label="Pick a Date"
                    onPress={showDatePicker}
                    variant="darkblue"
                    className="mt-5"
                  />
                </View>
              ) : (
                <Text className="mt-5 text-red-500">
                  Calendar permission not granted!
                </Text>
              )}
            </View>
            <Button
              variant="darkblue"
              label="Check Companion"
              onPress={() =>
                router.push('/form/companion-required/available-companion')
              }
              className="my-6"
            />
          </View>
        </Modal> */}

        <View className="flex-1 gap-8">
          <View className="mt-4 ">
            <Text className="font-PoppinsBold text-xl font-bold leading-[30px] text-black-950 dark:text-black-50">
              Add Photo & Set Value
            </Text>
            <Text className="font-inter font-medium leading-5 dark:text-black-300">
              Add traveler's flight details to connect with companion
            </Text>
          </View>
          <ScrollView className="mb-10 flex-1">
            <View className="flex-1 justify-between ">
              <View>
                <Text className="font-PoppinsRegular text-[16px] font-normal leading-[24px] text-black-950 dark:text-black-50">
                  Upload Travelers Photo
                </Text>
                <View className="mt-2 flex-row items-center gap-2">
                  <Controller
                    control={control}
                    name="travellersPhoto"
                    render={({ field: { value }, fieldState: { error } }) => (
                      <>
                        <ImagePick
                          image={value}
                          pickImage={travellerImagePick}
                          handleRemoveImage={() => {
                            setValue('travellersPhoto', null);
                          }}
                        />
                        {error?.message && (
                          <Text className="text-red-500">
                            {error.message?.toString()}
                          </Text>
                        )}
                      </>
                    )}
                  />
                </View>

                <View className="mt-6 gap-6">
                  <InputLabelled label="Upload Passport">
                    <Controller
                      control={control}
                      name="passportPhoto"
                      render={({ field: { value }, fieldState: { error } }) => (
                        <>
                          <InputText
                            onPress={passportImagePick}
                            placeholder={value ? value : 'Choose a Picture'}
                            iconSourceFirst={require('../../../../assets/images/upload.png')}
                            iconStyleFirst={{ height: 13.923, width: 15 }}
                            iconStyleSecond={{ height: 18, width: 15 }}
                            disabled={true}
                          />
                          {error?.message && (
                            <Text className="text-red-500">
                              {error.message?.toString()}
                            </Text>
                          )}
                        </>
                      )}
                    />

                    <Text className="mt-2 font-inter text-[11px] font-normal text-black-300 dark:text-black-100">
                      Passport details are for verification purpose only and it
                      will not be share with other users.
                    </Text>
                  </InputLabelled>

                  <InputLabelled label="Companion's Compensation Value (US$)">
                    <Controller
                      control={control}
                      name="companionCompensation"
                      render={({
                        field: { value, onChange },
                        fieldState: { error },
                      }) => (
                        <>
                          <InputText
                            value={value}
                            onChangeText={(text) => {
                              let doubleText = text.replace(/[^0-9.]/g, '');
                              const parts = doubleText.split('.');
                              if (parts.length > 2) {
                                doubleText =
                                  parts[0] + '.' + parts.slice(1).join('');
                              }
                              onChange(doubleText);
                            }}
                            placeholder=""
                          />
                          {error?.message && (
                            <Text className="text-red-500">
                              {error.message?.toString()}
                            </Text>
                          )}
                        </>
                      )}
                    />
                  </InputLabelled>
                  {errors.companionCompensation && (
                    <Text className="text-red-500">
                      {errors.companionCompensation.message?.toString()}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
        <View className="mb-4">
          <Button
            variant="secondary"
            label="Find Companion"
            onPress={onSubmit}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
