import { router } from 'expo-router';
import React from 'react';
import { Alert, Text, View } from 'react-native';

import BackButton from '@/components/back-button';
import BookingsCard from '@/components/bookings-card';
import { Button, FocusAwareStatusBar } from '@/components/ui';

export default function BookingDetails() {
  return (
    <View className="flex-1 dark:bg-primary-950">
      <FocusAwareStatusBar />
      <View className="gap-2.5 px-5 py-3 dark:bg-[#20202080]">
        <BackButton
          text="Back"
          text2="Booking Details"
          text3="Help"
          onPress={() => {
            router.back();
          }}
          onPress3={() => Alert.alert('Function not implemented yet')}
        />
        <BookingsCard
          date="17 Jan,2024"
          boarding="LGA"
          alighting="RTM"
          boardingTiming="02:05 PM"
          travelTiming="1:30 hr AM"
          alightingTiming="05:2 PM"
          TravelerName="Shreesh"
          TravelerDetails="Male, 20yrs, India"
          text="Paid"
          className="dark:bg-secondary-50"
          classNameText="dark:text-secondary-700"
          profileImage={require('../../../assets/images/avatar.png')}
          RuppeColor="dark:text-secondary-700"
        />
      </View>
      <View className="flex-1 gap-8 px-5 ">
        <View className="mt-8 gap-6 rounded-xl border border-primary-900  p-5 dark:bg-primary-950">
          {/* Row First */}
          <View className="gap-3.5   border-b border-primary-900 pb-4">
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                {' '}
                Payment on
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                9 Decemeber, 2023 | 10:00 AM
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                {' '}
                Booked Date
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                19 December, 2023
              </Text>
            </View>
          </View>
          {/* Row Second */}
          <View className="gap-8  border-b border-primary-900 pb-4">
            <View className="gap-3.5">
              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                  Amount
                </Text>
                <Text className="font-inter text-sm font-semibold dark:text-black-50">
                  {' '}
                  $90.50
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                  {' '}
                  Tax
                </Text>
                <Text className="font-inter text-sm font-semibold dark:text-black-50">
                  {' '}
                  $5.00
                </Text>
              </View>
            </View>

            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Total
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                $95.50
              </Text>
            </View>
          </View>
          {/* Row Thired */}
          <View className="gap-3.5 ">
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Name
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                Raj Kumar
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Phone Number
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                +91 8949952520
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Transaction ID
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                #IRM878748G548
              </Text>
            </View>
          </View>
        </View>
        <Button
          variant="secondary"
          label="Download Invoice"
          onPress={() => {}}
        />
      </View>
    </View>
  );
}
