import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, View } from 'react-native';

import MatchedRequestComponent from '@/components/matched-request-component';

import InviteModal from './Invite-modal';

const MatchedRequest = () => {
  return (
    <View className="flex-1">
      <ScrollView
        className=" mb-20 flex-1 px-5"
        showsVerticalScrollIndicator={false}
      >
        <InviteModal />

        <FlashList
          showsVerticalScrollIndicator={false}
          data={Data}
          keyExtractor={(item) => item.id.toString()}
          estimatedItemSize={50}
          renderItem={({ item }) => (
            <MatchedRequestComponent
              RatingNum={item.RatingNum}
              title={item.title}
              subtitle={item.subtitle}
              onPressChat={() => router.push('/(app)/chat')}
              onPressProfile={() => router.push('/chat-screens/profile')}
            />
          )}
        />
      </ScrollView>
    </View>
  );
};

export default MatchedRequest;

const Data = [
  {
    id: 1,
    RatingNum: '101',
    title: 'Kaushiki',
    subtitle: 'Female, 25yrs, Indian',
  },
  {
    id: 2,
    RatingNum: '100',
    title: 'Gargi',
    subtitle: 'Female, 20yrs, Pakistan',
  },
  {
    id: 3,
    RatingNum: '90',
    title: 'Mandakini',
    subtitle: 'Female, 20yrs, USA',
  },
  {
    id: 4,
    RatingNum: '99',
    title: 'Shivangi',
    subtitle: 'Female, 20yrs, USA',
  },
  {
    id: 5,
    RatingNum: '80',
    title: 'Shiva',
    subtitle: 'Female, 20yrs, China',
  },
];
