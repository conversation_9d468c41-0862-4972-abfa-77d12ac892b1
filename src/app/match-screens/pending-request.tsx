import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';

import PendingRequestComponent from '@/components/pending-request-component';

import InviteModal from './Invite-modal';
const { height, width } = Dimensions.get('screen');

const PendingRequest = () => {
  return (
    <View className="flex-1">
      {Data.length === 0 ? (
        <View className="px-5">
          <View className="my-16 items-center justify-center">
            <Image
              source={require('../../../assets/images/pananoreq.png')}
              style={{ height: height * 0.183, width: width * 0.651 }}
            />
          </View>
          <View className="items-center justify-center">
            <Text className="mb-3 text-center font-inter text-base font-medium text-black-100">
              No Request sent or recevied
            </Text>
            <Text className="mb-6 text-center font-inter text-base font-medium text-black-100">
              Send request to companion to connect with enjoy the journey
              together without any hustle.
            </Text>
          </View>
          <Pressable
            className="self-center rounded-xl border border-charcoal-50 bg-primary-950 px-6 py-3.5"
            onPress={() => router.push('/(app)/home')}
          >
            <Text className="font-inter text-base font-medium text-charcoal-50">
              Send Request
            </Text>
          </Pressable>
        </View>
      ) : (
        <ScrollView
          className=" mb-20 flex-1 px-5"
          showsVerticalScrollIndicator={false}
        >
          <InviteModal />

          <FlashList
            showsVerticalScrollIndicator={false}
            data={Data}
            keyExtractor={(item) => item.id.toString()}
            estimatedItemSize={50}
            renderItem={({ item }) => (
              <PendingRequestComponent
                RatingNum={item.RatingNum}
                title={item.title}
                subtitle={item.subtitle}
                onPressAccept={() =>
                  // router.push('/(app)/chat')
                  Alert.alert('Function not implemented yet')
                }
                onPressReject={() =>
                  // router.push('/(app)/chat')
                  Alert.alert('Function not implemented yet')
                }
              />
            )}
          />
        </ScrollView>
      )}
    </View>
  );
};

export default PendingRequest;

const Data = [
  {
    id: 1,
    RatingNum: '101',
    title: 'Kaushiki',
    subtitle: 'Female, 25yrs, Indian',
  },
  {
    id: 2,
    RatingNum: '100',
    title: 'Gargi',
    subtitle: 'Female, 20yrs, Pakistan',
  },
  {
    id: 3,
    RatingNum: '90',
    title: 'Mandakini',
    subtitle: 'Female, 20yrs, USA',
  },
  {
    id: 4,
    RatingNum: '99',
    title: 'Shivangi',
    subtitle: 'Female, 20yrs, USA',
  },
  {
    id: 5,
    RatingNum: '80',
    title: 'Shiva',
    subtitle: 'Female, 20yrs, China',
  },
];
