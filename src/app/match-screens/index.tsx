import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import React from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import MatchedRequest from './matched-request';
import PendingRequest from './pending-request';

const Tab = createMaterialTopTabNavigator();

const initialLayout = {
  width: Dimensions.get('window').width,
};

interface TabLabelProps {
  focused: boolean;
  title: string;
}

const TabLabel: React.FC<TabLabelProps> = ({ focused, title }) => {
  return (
    <Text style={focused ? styles.activeLabel : styles.inactiveLabel}>
      {title}
    </Text>
  );
};

const CustomTabBarIndicator = ({ layout, navigation }: any) => {
  const activeIndex = navigation.getState().index;
  const tabWidth = layout.width / 2;
  const indicatorPercentage = 0.6;
  const indicatorWidth = tabWidth * indicatorPercentage;

  const leftPosition = activeIndex * tabWidth + (tabWidth - indicatorWidth) / 2;

  return (
    <View style={styles.tabIndicatorContainer}>
      <View
        style={[
          styles.tabIndicator,
          { left: leftPosition, width: indicatorWidth },
        ]}
      />
    </View>
  );
};

const CustomTabBar = (props: {
  state: { routes: { name: string }[]; index: number };
  layout: any;
  navigation: any;
}) => {
  return (
    <View style={styles.tabBar}>
      {props.state.routes.map((route, index) => {
        const isFocused = index === props.state.index;

        return (
          <TouchableOpacity
            style={styles.tabItem}
            key={index}
            onPress={() => {
              props.navigation.navigate(route.name);
            }}
          >
            <TabLabel focused={isFocused} title={route.name} />
          </TouchableOpacity>
        );
      })}
      <CustomTabBarIndicator
        layout={props.layout}
        navigation={props.navigation}
      />
    </View>
  );
};

export default function Index() {
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      initialLayout={initialLayout}
    >
      <Tab.Screen
        name="Pending Request"
        component={PendingRequest}
        options={{ tabBarLabel: 'My Listings' }}
      />
      <Tab.Screen
        name="Matched Request"
        component={MatchedRequest}
        options={{ tabBarLabel: 'Favorites' }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#020717',
    height: 40,
    position: 'relative',
    flexDirection: 'row',
    marginTop: 15,
    // marginHorizontal:20
  },
  activeLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'bold',
    fontFamily: 'AirbnbW_Md',
    color: '#F2F2F2',
  },
  inactiveLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'normal',
    fontFamily: 'AirbnbW_Bk',
    color: '#F2F2F2',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIndicatorContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 2,
    backgroundColor: '#071952',
  },
  tabIndicator: {
    backgroundColor: '#CCCCCC',
    height: '100%',
    position: 'absolute',
    bottom: 0,
  },
});
