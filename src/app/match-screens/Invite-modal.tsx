import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Modal,
  Pressable,
  Text,
  View,
} from 'react-native';
const { height, width } = Dimensions.get('screen');

const InviteModal = () => {
  const [openModal, setOpenModal] = useState(false);
  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="flex-1 items-center justify-end  dark:bg-bgmodal">
          <View className="mt-30 w-full rounded-t-[32px] px-5 py-8 dark:bg-primary-950">
            <View className="gap-2">
              <View className="flex-row justify-between">
                <Image
                  source={require('../../../assets/images/star.png')}
                  resizeMode="contain"
                  style={{ height: 60, width: 60 }}
                />
                <Pressable
                  onPress={() => {
                    setOpenModal(false);
                  }}
                  className="p-2"
                >
                  <Image
                    source={require('../../../assets/images/cross.png')}
                    resizeMode="contain"
                    style={{ height: 24, width: 24 }}
                  />
                </Pressable>
              </View>

              <View className="items-center gap-2">
                <Text className="font-PoppinsMedium text-[14px] font-medium leading-5 text-black-0 dark:text-black-300">
                  Invite Companion
                </Text>
                <Text className="font-inter text-[20px] font-bold leading-7 text-black-50 dark:text-black-0">
                  Invite Companion and Earn
                </Text>
              </View>

              <View className="w-full flex-row items-center justify-between">
                <View className="mr-5 flex-row items-center">
                  <Image
                    source={require('../../../assets/images/coines.png')}
                    resizeMode="contain"
                    className="mr-1 size-12"
                  />
                  <Image
                    source={require('../../../assets/images/50coins.png')}
                    resizeMode="contain"
                    style={{ height: 34, width: 101 }}
                  />
                </View>
                <Text className="flex-1 flex-wrap font-inter text-[14px] font-normal dark:text-black-300">
                  For every companion or traveler who create free account
                </Text>
              </View>

              <View className="my-6">
                <Image
                  source={require('../../../assets/images/cuate1.png')}
                  style={{ height: height * 0.2382, width: width * 0.8337 }}
                />
                <Image
                  source={require('../../../assets/images/sparkle.png')}
                  className="size-10 self-end"
                />
              </View>

              <View className="my-6 items-center self-center rounded-2xl bg-primary-900 px-5 py-2.5">
                <View className="flex-row items-center">
                  <Text className="font-inter text-xs font-medium text-black-50">
                    Invite code
                  </Text>
                  <Text className="font-inter text-xs font-medium text-black-50">
                    {' '}
                    : 54920FD11
                  </Text>
                  <Pressable>
                    <Image
                      source={require('../../../assets/images/copy.png')}
                      className="ml-1 size-3"
                    />
                  </Pressable>
                </View>
              </View>

              <View className="w-full flex-row items-center justify-evenly">
                <Pressable
                  onPress={() => {
                    Alert.alert('Function not Implemented yet');
                  }}
                  className="w-[45%] flex-row items-center justify-center rounded-xl bg-black-0 py-3.5 font-inter font-bold"
                >
                  <Image
                    source={require('../../../assets/images/whatsapp.png')}
                    className="mr-2 size-5"
                  />
                  <Text className="font-inter text-sm font-medium text-blue">
                    Invite
                  </Text>
                </Pressable>

                <Pressable
                  onPress={() => {
                    Alert.alert('Function not Implemented yet');
                  }}
                  className="w-[45%] flex-row items-center justify-center rounded-xl bg-[#20202033] py-3.5 font-inter font-bold"
                >
                  <Image
                    source={require('../../../assets/images/shareyo.png')}
                    className="mr-2 size-5"
                  />
                  <Text className="font-inter text-sm font-medium text-black-50">
                    More Options
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  return (
    <View>
      {renderModal()}
      <View className="mt-5 flex-row gap-1 rounded-[16px] bg-black-0 p-4  dark:bg-black-950">
        <View className="flex-1">
          <Text className="font-PoppinsBold text-[16px] font-bold leading-6 text-black-950 dark:text-black-0">
            Invite companion on Thedal
          </Text>
          <Text className="font-inter text-[14px] font-medium leading-5 text-black-950 dark:text-black-0">
            Invite companion/ traveler and get 50 cashback points
          </Text>
        </View>
        <Pressable
          onPress={() => {
            setOpenModal(!openModal);
          }}
        >
          <Image
            source={require('../../../assets/images/share.png')}
            resizeMode="contain"
            style={{ height: 50, width: 50 }}
          />
        </Pressable>
      </View>
    </View>
  );
};

export default InviteModal;
