import { Stack } from 'expo-router';
const WalletLayout = () => {
  return (
    <Stack initialRouteName="index">
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="add-money-2-wallet" options={{ headerShown: false }} />
      <Stack.Screen name="connect-account" options={{ headerShown: false }} />
      <Stack.Screen name="successful" options={{ headerShown: false }} />
    </Stack>
  );
};
export default WalletLayout;
