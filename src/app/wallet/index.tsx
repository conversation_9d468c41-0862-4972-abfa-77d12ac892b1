import { type BottomSheetModal } from '@gorhom/bottom-sheet';
import { useFocusEffect } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { format } from 'date-fns';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  ImageBackground,
  Platform,
  Pressable,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';
import { type Models, Query } from 'react-native-appwrite';
import { showMessage } from 'react-native-flash-message';

import BackButton from '@/components/back-button';
import { Modal, useModal } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { account, databases } from '@/lib/appwrite';
import { Env } from '@/lib/env';
const { height, width } = Dimensions.get('screen');

const WalletPage = () => {
  // Properly typing the ref with BottomSheetModal type
  const bottomSheetModalRef = useRef<BottomSheetModal>(null); // Type the ref
  const [transactions, setTransactions] =
    useState<Models.DocumentList<Models.Document>>();
  const [loading, setLoading] = useState(false);
  const { ref, present, dismiss } = useModal();
  const {
    ref: createCardRef,
    present: presentCreateCard,
    dismiss: dismissCreateCard,
  } = useModal();

  // UseEffect equivalent with useFocusEffect to open the BottomSheetModal when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (bottomSheetModalRef.current) {
        bottomSheetModalRef.current.present(); // This will open the BottomSheetModal when screen is focused
      }
    }, [])
  );

  const getWalletTransactions = async () => {
    const user = await account.get();
    try {
      const orders = await databases.listDocuments(
        Env.COLLECTION_ID,
        Env.WALLET_ORDERS_COLLECTION_ID ?? 'orders',
        [Query.equal('userId', user.$id), Query.orderDesc('$createdAt')]
      );
      // {userId: string; orderId: string; amountInCents: number; text: string; }
      console.log('transactions', orders);
      setTransactions(orders);
    } catch (error) {
      showMessage({
        message: 'Error fetching transactions',
        description: error?.message ?? 'Error fetching wallet transactions',
        type: 'danger',
      });
      console.log('error', error);
    }
  };

  useEffect(() => {
    getWalletTransactions();
  }, []);

  // Function to open the modal
  const openCreateCardModal = () => {
    presentCreateCard();
  };

  // Function to close the modal
  const closeCreateCardModal = () => {
    dismissCreateCard();
  };

  const handleNavigate = () => {
    bottomSheetModalRef.current?.close();
    router.push('/wallet/add-money-2-wallet');
  };

  const totalAmount = transactions?.documents.reduce(
    (acc, curr) => acc + curr.amountInCents,
    0
  );

  return (
    <View className="flex-1">
      <ImageBackground
        source={require('../../../assets/images/globalmap.png')}
        resizeMode="contain"
        className="my-5"
      >
        <View className="px-5">
          {/* Top heading */}
          <View className="flex-row items-center justify-between">
            <BackButton
              text="Back"
              text2="Wallet"
              onPress={() => {
                router.back();
              }}
            />

            <Text>Wallet</Text>
            <Text />
          </View>

          {/* Wallet amount */}
          {Platform.OS === 'ios' ? (
            <BlurView
              intensity={10}
              style={{ backgroundColor: '#1539AF66', borderRadius: 12 }}
              className="mt-6 flex-row items-center justify-between px-4 py-2.5"
            >
              <View>
                <Text className="font-PoppinsBold text-4xl font-bold text-black-0">
                  $ {totalAmount ? totalAmount / 100 : 0}
                </Text>
                <Text className="mt-1 font-PoppinsRegular text-sm font-normal text-black-100">
                  Wallet Balance
                </Text>
              </View>

              <Pressable
                className="rounded-xl border border-borderdark bg-[#FDFDFD1A] px-6 py-3.5"
                onPress={handleNavigate}
              >
                <Text className="font-inter text-base font-medium text-black-0">
                  Add money
                </Text>
              </Pressable>
            </BlurView>
          ) : Platform.OS === 'android' ? (
            <View
              style={{ backgroundColor: '#1539AF66', borderRadius: 12 }}
              className="mt-6 flex-row items-center justify-between px-4 py-2.5"
            >
              <View>
                <Text className="font-PoppinsBold text-4xl font-bold text-black-0">
                  $ {totalAmount ? totalAmount / 100 : 0}
                </Text>
                <Text className="mt-1 font-PoppinsRegular text-sm font-normal text-black-100">
                  Wallet Balance
                </Text>
              </View>

              <Pressable
                className="rounded-xl border border-borderdark bg-[#FDFDFD1A] px-6 py-3.5"
                onPress={handleNavigate}
              >
                <Text className="font-inter text-base font-medium text-black-0">
                  Add money
                </Text>
              </Pressable>
            </View>
          ) : null}
        </View>

        {/* <View className="flex-1 items-center justify-center">
          <ScrollView
            // horizontal
            showsHorizontalScrollIndicator={false}
            className="w-full flex-1 px-5 pt-6"
          >
            <View style={styles.cardContainer}>
              <LinearGradient
                colors={['#051410', 'rgba(5, 20, 16, 0)']}
                style={styles.gradientLayer}
                start={{ x: 1, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <LinearGradient
                  colors={['#061546', 'rgba(6, 21, 70, 0)']}
                  style={styles.gradientLayer}
                  start={{ x: 1, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <LinearGradient
                    colors={['rgba(21, 57, 175, 0.40)', 'rgba(21, 57, 175, 0)']}
                    style={styles.gradientLayer}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <View className="rounded- px-6 py-8">
                      <View className="mb-7 flex-row items-center justify-between">
                        <Text className="font-PoppinsBold text-lg font-bold text-black-0">
                          Tannu
                        </Text>
                        <Pressable className="self-end rounded-md bg-black-0/10 px-2 py-1">
                          <Text className="font-inter text-[10px] font-semibold text-black-0">
                            Default
                          </Text>
                        </Pressable>
                      </View>

                      <View className="mb-2">
                        <Text className="font-PoppinsRegular text-sm font-normal text-black-0">
                          01/2021
                        </Text>
                      </View>

                      <View className="flex-row items-center justify-between">
                        <Text className="font-PoppinsRegular text-sm font-normal text-black-0">
                          **** **** **** 9025
                        </Text>
                        <View className="items-center">
                          <Image
                            source={require('../../../assets/images/mastercard.png')}
                            className="h-7 w-10"
                            resizeMode="contain"
                          />
                          <Text className="text-[8px] font-normal text-black-0">
                            mastercard
                          </Text>
                        </View>
                      </View>
                    </View>
                  </LinearGradient>
                </LinearGradient>
              </LinearGradient>
            </View>

            <View
              className="ml-4 self-start bg-black-0 px-4 py-6"
              style={styles.card2}
            >
              <View className="mb-7 flex-row items-center justify-between">
                <Text className="font-PoppinsBold text-lg font-bold text-primary-800">
                  Tannu
                </Text>
                <Pressable
                  className="self-end rounded-md py-1"
                  onPress={() => Alert.alert('Function not implemented yet')}
                >
                  <Text className="font-inter text-[10px] font-semibold text-black-900">
                    Set as Default
                  </Text>
                </Pressable>
              </View>

              <View className="mb-2">
                <Text className="font-PoppinsRegular text-sm font-normal text-black-700">
                  01/2021
                </Text>
              </View>

              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal text-black-700">
                  **** **** **** 9025
                </Text>
                <View className="items-center">
                  <Image
                    source={require('../../../assets/images/mastercard.png')}
                    className="h-7 w-10"
                    resizeMode="contain"
                  />
                  <Text className="text-[8px] font-normal text-black-700">
                    mastercard
                  </Text>
                </View>
              </View>
            </View>

            <Pressable
              className="ml-4 flex-row items-center justify-center rounded-3xl bg-black-0 px-4 py-6"
              style={{ height: height * 0.147, width: width * 0.307 }}
              onPress={openCreateCardModal}
            >
              <Image
                source={require('../../../assets/images/addcardicon.png')}
                className="mr-2 size-6"
                resizeMode="contain"
              />
              <Text className="font-PoppinsMedium text-sm font-medium text-primary-800">
                Add Card
              </Text>
            </Pressable>
            <View className="mr-10" />
          </ScrollView>
        </View> */}
      </ImageBackground>

      {/* Bottom Sheet Modal */}
      {/* <Modal
        ref={ref}
        snapPoints={['54%', '80%']}
        backgroundStyle={{ backgroundColor: '#030303' }}
        handleIndicatorStyle={{
          backgroundColor: 'gray',
          width: 82,
          height: 5,
          borderRadius: 100,
        }}
        enablePanDownToClose={false}
      > */}
      <View className="flex-1 px-5">
        <View className="mb-4 flex-row items-center justify-between">
          <Text className="font-PoppinsMedium text-xl font-medium text-black-100">
            Wallet History
          </Text>
          {/* <Pressable>
            <Text className="font-inter text-base font-semibold text-black-50">
              View More
            </Text>
          </Pressable> */}
        </View>
        <FlashList
          data={transactions?.documents}
          showsVerticalScrollIndicator={false}
          estimatedItemSize={50}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={getWalletTransactions}
            />
          }
          renderItem={({ item }) => (
            <>
              <Pressable className="mb-3 flex-row items-center justify-between border-b-[0.55px] border-secondary-750 px-3 py-2">
                <View className="flex-row items-center">
                  {/* <Image
                    source={item.source}
                    resizeMode="contain"
                    className="mr-2 size-8"
                    tintColor={'#1E1E1E'}
                  /> */}
                  <View>
                    <Text className="font-inter text-sm text-black-300">
                      {item.text}{' '}
                    </Text>
                    <Text className="font-inter text-xs text-black-300">
                      {format(new Date(item.$createdAt), 'dd MMM yyyy hh:mm a')}
                    </Text>
                  </View>
                </View>
                {item.amountInCents > 0 ? (
                  <View className="flex-row items-center rounded-md bg-chatcardbg p-1">
                    <Image
                      source={require('../../../assets/images/arrowleft.png')}
                      className="mr-1 size-5"
                    />
                    <Text className="font-inter text-base font-semibold text-secondary-650">
                      $ {item.amountInCents / 100}
                    </Text>
                  </View>
                ) : (
                  <View className="flex-row items-center rounded-md bg-chatcardbg p-1">
                    <Image
                      source={require('../../../assets/images/arrowright.png')}
                      className="mr-1 size-5"
                    />
                    <Text className="font-inter text-base font-semibold text-red-500">
                      {item.amountInCents / 100}
                    </Text>
                  </View>
                )}
              </Pressable>
              {item.add && (
                <View className="mb-3">
                  <Image
                    source={item.add}
                    style={{ height: height * 0.159, width: width * 0.907 }}
                  />
                </View>
              )}
            </>
          )}
        />
      </View>
      {/* </Modal> */}
      {/* Bottom Sheet Modal 1*/}
      {/* <ScrollView className="flex-1" showsVerticalScrollIndicator={false}> */}
      <Modal
        ref={createCardRef}
        title="Add a new Card"
        snapPoints={['54%', '80%']}
        backgroundStyle={{ backgroundColor: '#030303' }}
      >
        <View className="flex-1">
          <View className="w-full rounded-t-2xl bg-primary-950 px-5 py-8">
            {/* <Pressable onPress={closeCreateCardModal}>
              <Image
                source={require('../../../assets/images/cancel.png')}
                className="size-6"
              />
            </Pressable> */}

            <View className="mb-5">
              {/* <Text className="my-5 font-inter text-xl font-semibold text-black-50">
                Add a new Card
              </Text> */}
              <View>
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  Card Number
                </Text>
                <TextInput
                  placeholder="xxxxxxxxxxxxxxx"
                  keyboardType="numeric"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
            </View>

            <View className="mb-5 flex-row items-center justify-center">
              <View className="mr-5 flex-1">
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  Expiry Date
                </Text>
                <TextInput
                  placeholder="MM/YY"
                  keyboardType="numeric"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
              <View className="flex-1">
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  CVC/CVV
                </Text>
                <TextInput
                  placeholder="***"
                  keyboardType="numeric"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
            </View>

            <View className="mb-5">
              <View>
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  Card Holder Name
                </Text>
                <TextInput
                  placeholder="enter your name"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
            </View>

            <Button
              variant="darkblue"
              label="Add New Card"
              onPress={function (): void {
                throw new Error('Function not implemented.');
              }}
            />
          </View>
        </View>
      </Modal>
      {/* </ScrollView> */}
    </View>
  );
};

export default WalletPage;

const styles = StyleSheet.create({
  gradientLayer: {
    flex: 1,
    borderRadius: 24,
    // width: width * 0.5581,
    // height: height * 0.1941,
  },
  cardContainer: {
    backgroundColor: '#00071D33',
    borderRadius: 24,
    width: width * 0.5581, // Adjust the width according to your design
    height: height * 0.1941, // Adjust the height according to your design
    // Shadow for iOS
    shadowColor: '#2A51CF', // Shadow color with transparency
    shadowOffset: { width: 0, height: 9 }, // Horizontal and vertical offset
    shadowOpacity: 1, // Full opacity for shadow
    shadowRadius: 49, // Blur radius
    // Elevation for Android (to show shadow)
    elevation: 12, // Elevation (shadow size on Android)
  },
  card2: {
    backgroundColor: '#FDFDFD', // background color
    borderRadius: 24, // border radius
    padding: 20, // padding inside the card
    shadowColor: '#5A5A5A33', // shadow for iOS
    shadowOffset: { width: 0, height: 3 }, // shadow offset
    shadowOpacity: 1, // shadow opacity
    shadowRadius: 9, // shadow blur radius
    elevation: 3, // shadow for Android
    width: width * 0.5581,
    // height: height * 0.1465,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end', // Position the modal content at the bottom of the screen
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Background dimming effect
  },
});

const Data = [
  {
    id: 1,
    details: 'Received on $500 / 24 July',
    money: '$40.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 2,
    details: 'Received on $500 / 24 July',
    money: '$43.23',
    type: 'send',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 3,
    details: 'Received on $500 / 24 July',
    money: '$84.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 4,
    details: 'Received on $500 / 24 July',
    money: '$74.23',
    type: 'send',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 5,
    details: 'Received on $500 / 24 July',
    money: '$65.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 6,
    details: 'Received on $500 / 24 July',
    money: '$87.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
    add: require('../../../assets/images/banner.png'),
  },
];
