import { Env } from '@env';
import {
  initPaymentSheet,
  presentPaymentSheet,
  StripeProvider,
} from '@stripe/stripe-react-native';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { TextInput } from 'react-native-gesture-handler';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';
import { useWalletPayment } from '@/hooks/use-wallet-payment';

const AddMoney2WalletPage = () => {
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const { createPaymentIntent, customer } = useWalletPayment();

  const initializePaymentSheet = async () => {
    setLoading(true);
    createPaymentIntent.mutate(parseFloat(amount), {
      onSuccess: async (data) => {
        const { paymentIntent, ephemeralKey } = data;
        const { error } = await initPaymentSheet({
          customerId: customer.id,
          paymentIntentClientSecret: paymentIntent,
          merchantDisplayName: 'TheDal',
          customerEphemeralKeySecret: ephemeralKey,
          returnURL: 'com.nextflytech.thedal://stripe-redirect',
          // merchantIdentifier: 'merchant.identifier',
          // urlScheme: 'your-url-scheme',
        });
        if (error) {
          showMessage({
            message: 'Payment Failed',
            description: error.message,
            type: 'danger',
          });
          setLoading(false);
          return;
        }

        const { error: presentError } = await presentPaymentSheet();
        if (presentError) {
          showMessage({
            message: 'Payment Failed',
            description: presentError.message,
            type: 'danger',
          });
          setLoading(false);
          return;
        }
        router.replace('/wallet/successful');
      },
      onError: (error) => {
        setLoading(false);
        showMessage({
          message: 'Payment Failed',
          description: error.message,
          type: 'danger',
        });
      },
    });
  };

  return (
    <StripeProvider
      publishableKey={Env.STRIPE_PUBLISHABLE_KEY}
      // merchantIdentifier="merchant.identifier" // required for Apple Pay
      // urlScheme="your-url-scheme" // required for 3D Secure and bank redirects
    >
      <KeyboardAvoidingView
        // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex flex-1 flex-col justify-between px-5"
      >
        <View>
          {/* Top heading */}
          <View className="flex-row items-center justify-between">
            <BackButton
              text="Back"
              text2="Add money to wallet"
              onPress={() => {
                router.back();
              }}
            />
            <Text />
          </View>

          {/* enter amount part */}
          <View className="mt-16 px-4">
            <View className="items-center justify-center border-b border-green px-5">
              <Text className="mb-5 font-PoppinsRegular text-lg font-normal text-black-300">
                Enter Amount
              </Text>
              <View className="mb-4 flex w-2/5 flex-row items-center justify-center ">
                <Text className="font-Poppins text-4xl text-black-200">$</Text>
                <TextInput
                  className="my-6 w-full rounded-xl px-5 font-PoppinsBold text-4xl font-bold leading-[40px] text-black-200"
                  keyboardType="numeric"
                  value={amount}
                  onChangeText={setAmount}
                  placeholder="0.00"
                />
              </View>
            </View>
            {/* 
            <View className="items-center justify-center">
              <Text className="mb-5 mt-4 font-inter text-lg font-normal text-black-100">
                From
              </Text>

              <Pressable className="w-full flex-row items-center justify-between rounded-lg bg-blue px-6 py-3">
                <View className="flex-row items-center">
                  <View className="mr-2.5 items-center">
                    <Image
                      source={require('../../../assets/images/mastercard.png')}
                      className="h-7 w-10"
                      resizeMode="contain"
                    />
                    <Text className="text-[8px] font-normal text-black-0">
                      mastercard
                    </Text>
                  </View>
                  <Text className="font-PoppinsRegular text-base font-normal text-charcoal-50">
                    XXXX XXXX XXXX 9025
                  </Text>
                </View>
                <AntDesign name="down" size={16} color="#FDFDFD" />
              </Pressable>
            </View> */}
          </View>
        </View>

        <View className="mb-8 w-full">
          <Button
            variant="darkblue"
            loading={loading}
            label={loading ? 'Processing...' : 'Proceed'}
            onPress={initializePaymentSheet}
            // onPress={handleProceed}
            disabled={loading || !amount}
          />
        </View>
      </KeyboardAvoidingView>
    </StripeProvider>
  );
};

export default AddMoney2WalletPage;
