import { router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import React, { useEffect, useState } from 'react';
import { Image, Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';
import {
  type IStripeConnectAccountResponse,
  useStripeConnect,
} from '@/hooks/use-stripe-connect';

const ConnectAccountPage = () => {
  const [loading, setLoading] = useState(false);
  const [accountId, setAccountId] = useState<string | null>(null);
  const [accountDetails, setAccountDetails] =
    useState<IStripeConnectAccountResponse | null>(null);
  const [accountLinkLoading, setAccountLinkLoading] = useState(false);
  const [accountLink, setAccountLink] = useState<string | null>(null);
  const { createConnectAccount, getStripeConnectAccount, createAccountLink } =
    useStripeConnect();

  const fetchAccountDetails = async () => {
    try {
      const details = await getStripeConnectAccount.mutateAsync();
      console.log('Account details:', details);
      setAccountDetails(details);
    } catch (error: any) {
      console.log('Error fetching account details:', error);
      // If we can't fetch details, we'll just show the account ID
    }
  };

  const handleGenerateAccountLink = async () => {
    if (!accountId) return;

    setAccountLinkLoading(true);
    try {
      const { accountLink: url, success } =
        await createAccountLink.mutateAsync();
      console.log('Account link URL:', url);

      // Open the URL in the browser
      if (success) {
        await WebBrowser.openBrowserAsync(url);
      }
    } catch (error: any) {
      console.log('Error generating account link:', error);
      showMessage({
        message: 'Failed to Generate Link',
        description: error.message,
        type: 'danger',
      });
    } finally {
      setAccountLinkLoading(false);
    }
  };

  const handleStripeConnect = async () => {
    setLoading(true);
    try {
      // The createConnectAccount function will check for existing account ID in preferences
      const resp = await createConnectAccount.mutateAsync();
      console.log('response is', resp);

      if (resp.success && resp.accountId) {
        setAccountId(resp.accountId);

        // If we have an account ID, fetch the account details
        await fetchAccountDetails();
      }

      // await Linking.openURL(url);
      // router.replace('/wallet');
    } catch (error: any) {
      console.log('error is', error);
      showMessage({
        message: 'Connection Failed',
        description: error.message,
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Using an IIFE to avoid the dependency warning
    (async () => {
      await handleStripeConnect();
    })();
    // We only want to run this once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <View className="flex-1 px-5">
      <View>
        {/* Top heading */}
        <View className="flex-row items-center justify-between">
          <BackButton
            text="Back"
            text2="Connect Stripe Account"
            onPress={() => router.back()}
          />
          <Text />
        </View>

        {/* Content */}
        <View className="mt-16 items-center justify-center px-4">
          <Image
            source={require('../../../assets/images/stripe-connect.png')}
            className="size-32"
            resizeMode="contain"
          />
          <Text className="mt-8 font-PoppinsBold text-2xl font-bold text-black-200">
            {accountId
              ? 'Stripe Account Connected'
              : 'Connect Your Stripe Account'}
          </Text>
          {accountId ? (
            <View className="mt-4">
              <Text className="text-center font-PoppinsRegular text-base font-normal text-black-100">
                Your Stripe account is connected with ID:{' '}
                {accountId.substring(0, 8)}...
              </Text>

              {accountDetails && (
                <View className="mt-4">
                  {/* Show account status */}
                  <Text className="text-center font-PoppinsRegular text-base font-semibold text-black-100">
                    Account Status
                  </Text>

                  <Text className="mt-1 text-center font-PoppinsRegular text-sm text-black-100">
                    Card Payments Enabled:{' '}
                    {accountDetails.capabilties.card_payments === 'active'
                      ? '✅'
                      : '❌'}
                  </Text>

                  <Text className="mt-1 text-center font-PoppinsRegular text-sm text-black-100">
                    Transfers Enabled:{' '}
                    {accountDetails.capabilties.transfers === 'active'
                      ? '✅'
                      : '❌'}
                  </Text>

                  {/* Show pending requirements if any */}
                  {accountDetails.requirements &&
                    accountDetails.requirements.currently_due &&
                    accountDetails.requirements.currently_due.length > 0 && (
                      <View className="mt-4">
                        <Text className="text-center font-PoppinsRegular text-base font-semibold text-black-100">
                          Pending Requirements
                        </Text>

                        {accountDetails.requirements.currently_due.map(
                          (requirement, index) => (
                            <Text
                              key={index}
                              className="mt-1 text-center font-PoppinsRegular text-sm text-black-100"
                            >
                              • {requirement.replace(/_/g, ' ')}
                            </Text>
                          )
                        )}

                        <Text className="mt-2 text-center font-PoppinsRegular text-sm text-black-100">
                          Complete these steps to enable payments
                        </Text>
                      </View>
                    )}
                </View>
              )}
            </View>
          ) : (
            <Text className="mt-4 text-center font-PoppinsRegular text-base font-normal text-black-100">
              To receive payments as a companion, you need to connect your
              Stripe account. This helps us process payments securely.
            </Text>
          )}
        </View>
      </View>

      <View className="mb-8 mt-auto flex w-full flex-col gap-5 space-y-4">
        <Button
          variant="darkblue"
          loading={loading}
          label={
            loading
              ? 'Processing...'
              : accountId
                ? 'Refresh Account Details'
                : 'Connect Stripe Account'
          }
          onPress={accountId ? fetchAccountDetails : handleStripeConnect}
          disabled={loading || accountLinkLoading}
        />

        {accountId &&
          accountDetails &&
          accountDetails.requirements &&
          accountDetails.requirements.currently_due &&
          accountDetails.requirements.currently_due.length > 0 && (
            <Button
              variant="outline"
              loading={accountLinkLoading}
              label={
                accountLinkLoading
                  ? 'Generating Link...'
                  : 'Complete Account Setup'
              }
              onPress={handleGenerateAccountLink}
              disabled={loading || accountLinkLoading}
            />
          )}
      </View>
    </View>
  );
};

export default ConnectAccountPage;
