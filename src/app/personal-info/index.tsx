import { Env } from '@env';
import { zodResolver } from '@hookform/resolvers/zod';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Modal,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { ID } from 'react-native-appwrite';
import { type z } from 'zod';

import BackButton from '@/components/back-button';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import { Button, Switch } from '@/components/ui';
import { userSchema } from '@/form-schema/form-schema';
import { account, databases, storage } from '@/lib/appwrite';
import { getUserData, updateUserData } from '@/lib/user-data-service';
import { cn } from '@/lib/utils';
import { useLanguageStore } from '@/store/language-store';
import { UserType, userTypeStore } from '@/store/user-type-store';

const PersonalInfoPage = () => {
  // state for managing the user id
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [profilePicUrl, setProfilePicUrl] = useState<string | null>(null);

  //getting the type of user from zustand store
  const { userType } = userTypeStore((state) => {
    return {
      userType: state.userType,
    };
  });

  const {
    watch,
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: '',
      phoneNo: '',
      email: '',
      about: '',
      typeOfTraveller: 'SOLO',
      genderPreference: 'MALE',
      openToAllGender: false,
      languages: [],
      gender: 'MALE',
      bookingFor: 'Self',
      typeOfUser: userType,
    },
  });

  //using the language store to get the language that traveller speaks
  const { languageThatTravellerSpeaks, addLanguage } = useLanguageStore(
    (state) => {
      return {
        languageThatTravellerSpeaks: state.languageThatTravellerSpeaks,
        addLanguage: state.addLanguage,
      };
    }
  );

  //getting the details of the logged in user
  useEffect(() => {
    console.log('user type is', userType);
    async function userDetails() {
      try {
        //get the user account details
        const user = await account.get();
        console.log('user is', user);

        //set the basic account details
        reset({
          name: user.name,
          phoneNo: user.phone,
          email: user.email,
        });

        // try to get the user additional details
        try {
          const details = await databases.getDocument(
            '6711d07f002f03a35d08',
            '675aaac0000029cc416c',
            userType === UserType.TRAVELLER ? user.$id : `c-${user.$id}`
          );

          console.log('details exits already', details);
          // If languages exist in the database, then set it in the language store
          if (details.languages && Array.isArray(details.languages)) {
            details.languages.forEach((lang) => {
              addLanguage(lang.$id, lang.name);
            });
          }

          reset({
            name: user.name,
            phoneNo: user.phone,
            email: user.email,
            about: details.about,
            typeOfTraveller: details.typeOfTraveller,
            genderPreference: details.genderPreference,
            openToAllGender: details.openToAllGender,
            gender: details.gender,
            bookingFor: details.bookingFor,
            typeOfUser: userType,
          });
        } catch (error: any) {
          if (error.code === 404) {
            console.log('User details do not exist yet - will create on save');
            setUserId(user.$id);
          } else {
            // Handle other potential errors
            console.error('Error fetching user details:', error);
          }
        }
      } catch (err) {
        console.error('Error getting user account:', err);
      }
    }
    userDetails();
  }, []);

  const onSubmit = async (data: z.infer<typeof userSchema>) => {
    console.log('data is', data);
    setIsLoading(true);
    try {
      //update basic account details
      await account.updateName(data.name);
      await updateUserData({
        email: data.email ?? '',
      });

      const user = await account.get();
      const documentData = {
        about: data.about,
        typeOfTraveller: data.typeOfTraveller,
        gender: data.gender,
        openToAllGender: data.openToAllGender,
        genderPreference: data.genderPreference,
        bookingFor: 'Self',
        typeOfUser: userType,
        languages: languageThatTravellerSpeaks.map((i) => i.id) || [],
        UID: user.$id,
      };
      if (userId) {
        //create a new document if user details does not exist
        try {
          const loggedInUser = await databases.createDocument(
            '6711d07f002f03a35d08',
            '675aaac0000029cc416c',
            userType === UserType.TRAVELLER ? user.$id : `c-${user.$id}`,
            documentData
          );
          setUserId(null);
          console.log('user created successfully', loggedInUser);
        } catch (createError: any) {
          console.error('Create document error details:', createError);
          throw createError;
        }
      } else {
        //otherwise update the document
        try {
          const updateDocument = await databases.updateDocument(
            '6711d07f002f03a35d08',
            '675aaac0000029cc416c',
            userType === UserType.TRAVELLER ? user.$id : `c-${user.$id}`,
            documentData
          );
          console.log('Document updated successfully', updateDocument);
        } catch (updateError: any) {
          console.error('Update document error details:', updateError);
          throw updateError;
        }
      }
    } catch (error) {
      console.error('Error saving user details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onError = (errors: any) => {
    console.log(errors);
  };

  const PickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.5,
    });

    if (!result.canceled && result.assets.length > 0) {
      const image = result.assets[0];
      if (image) {
        const userData = await getUserData();
        if (userData.userProfile) {
          await storage
            .deleteFile(Env.STORAGE_BUCKET_ID, userData.userProfile)
            .catch((err) => {
              console.log('error in deleting user profile', err);
            });
          await updateUserData({
            userProfile: null,
            userProfileUrl: null,
          });
        }
        const uploadedUserPic = await storage
          .createFile(
            Env.STORAGE_BUCKET_ID,
            ID.unique(),
            {
              uri: image.uri,
              type: image.mimeType ?? '',
              name: image.fileName ?? '',
              size: image.fileSize ?? 0,
            },
            ['read("any")']
          )
          .catch((err) => {
            console.log('error in uploading user profile', err);
          });

        if (uploadedUserPic) {
          await updateUserData({
            userProfile: uploadedUserPic.$id,
          });
          try {
            const url = storage.getFileView(
              Env.STORAGE_BUCKET_ID,
              uploadedUserPic.$id
            );
            console.log('url is', url);
            setProfilePicUrl(url.href);
            await updateUserData({
              userProfileUrl: url.href,
            });
          } catch (err) {
            console.log('error in getting user profile', err);
          }
        }
      }
    }
  };

  const loadUserData = async () => {
    const userData = await getUserData();
    console.log('user data is', userData);
    if (userData.userProfileUrl) {
      setProfilePicUrl(userData.userProfileUrl);
    }
  };

  useEffect(() => {
    loadUserData();
  }, []);

  const [openModal, setOpenModal] = useState(false);
  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="flex-1  items-center justify-center px-5 dark:bg-bgmodal ">
          <View className="rounded-2xl  bg-white  px-5 py-6 dark:bg-black-950  ">
            <View className="gap-2.5">
              <Text className="font-PoppinsSemiBold text-xl  font-semibold text-black-950 dark:text-black-50">
                Change your profile photo
              </Text>

              <TouchableOpacity
                className="flex-row items-center gap-5 p-3"
                onPress={() => {
                  setOpenModal(false);
                  PickImage();
                }}
              >
                <Image
                  source={require('../../../assets/images/gallery.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 21.818 }}
                />
                <Text className="font-Poppins text-bold text-[16px] leading-[20px] text-black-0">
                  Choose photo
                </Text>
              </TouchableOpacity>

              <TouchableOpacity className="flex-row items-center gap-5 p-3">
                <Image
                  source={require('../../../assets/images/profile-camera.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 21.818 }}
                />
                <Text className="font-Poppins text-bold text-[16px] leading-[20px] text-black-0">
                  Take photo
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-row items-center gap-5 p-3"
                onPress={() => {
                  setOpenModal(false);
                }}
              >
                <Image
                  source={require('../../../assets/images/delete.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 21.818 }}
                />
                <Text className="font-Poppins text-bold text-[16px] leading-[20px] text-black-0">
                  Remove current photo
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View className="w-full flex-1 justify-between gap-6 bg-primary-950">
      <View className="flex-1 gap-6 ">
        <View className="w-full gap-2.5 rounded-b-2xl px-5 pb-6 pt-2 dark:bg-blue">
          <BackButton
            text="Back"
            onPress={() => {
              router.back();
            }}
            text2="Edit Profile"
            text3="Save"
          />

          <TouchableOpacity
            className="relative items-center"
            onPress={() => {
              setOpenModal(true);
            }}
          >
            <View>
              <View
                style={{
                  width: 80,
                  height: 80,
                  borderRadius: 40,
                  overflow: 'hidden',
                }}
              >
                <Image
                  source={
                    profilePicUrl
                      ? { uri: profilePicUrl }
                      : require('../../../assets/images/user.png')
                  }
                  contentFit="cover"
                  style={{ width: '100%', height: '100%' }}
                />
              </View>

              <TouchableOpacity onPress={PickImage}>
                <View className="absolute bottom-0 left-0 flex-row items-center gap-[3.47px] rounded-xl bg-black-950 px-[6.93px] py-[5.2px]">
                  <Image
                    source={require('../../../assets/images/camera.svg')}
                    contentFit="contain"
                    tintColor={'white'}
                    style={{ height: 12, width: 12 }}
                  />
                  <Text className="font-inter text-[9px] font-medium leading-3 dark:text-black-300">
                    {profilePicUrl ? 'Change Photo' : 'Add Photo'}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className=" flex-1 gap-5 bg-primary-950 px-5">
            {/* name */}
            <InputLabelled label="Name">
              <Controller
                control={control}
                name="name"
                render={({ field: { onChange, value } }) => (
                  <InputText
                    onChangeText={onChange}
                    value={value}
                    placeholder="Enter your name"
                  />
                )}
              />
            </InputLabelled>
            {errors.name && (
              <Text className="text-red-500">
                {errors.name.message?.toString()}
              </Text>
            )}

            {/* phone number */}
            <InputLabelled label="Phone Number">
              <Controller
                control={control}
                name="phoneNo"
                render={({ field: { value } }) => (
                  <InputText
                    className="border-transparent"
                    placeholder="Enter your phone number"
                    value={value}
                    editable={false}
                  />
                )}
              />
            </InputLabelled>
            {errors.phoneNo && (
              <Text className="text-red-500">
                {errors.phoneNo.message?.toString()}
              </Text>
            )}

            {/* email */}
            <InputLabelled label="Email">
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, value } }) => (
                  <InputText
                    placeholder="Enter your email"
                    autoCapitalize="none"
                    value={value}
                    onChangeText={onChange}
                  />
                )}
              />
            </InputLabelled>
            {errors.email && (
              <Text className="text-red-500">
                {errors.email.message?.toString()}
              </Text>
            )}

            {/* are you a traveller */}
            <InputLabelled label="Are you a ______ Traveler?">
              <Controller
                control={control}
                name="typeOfTraveller"
                render={({ field: { onChange, value } }) => (
                  <View className="mt-2.5 flex-row justify-between rounded-[12px] border border-black-100 border-borderdark bg-black-0 px-5 py-[14px] text-black-0 dark:bg-bgtextInput">
                    <Pressable
                      onPress={() => {
                        onChange('SOLO');
                      }}
                    >
                      <View
                        className={cn(
                          'flex-row gap-2 items-center px-2 py-2 ',
                          {
                            ' bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
                              value === 'SOLO',
                          }
                        )}
                      >
                        <Image
                          source={require('../../../assets/images/plane.png')}
                          contentFit="contain"
                          style={{ height: 12, width: 12 }}
                        />
                        <Text
                          className={cn(
                            'text-black-0 dark:text-black-300',
                            value === 'SOLO'
                              ? 'dark:text-black-200 '
                              : 'text-black-300'
                          )}
                        >
                          Solo
                        </Text>
                      </View>
                    </Pressable>

                    <Pressable
                      onPress={() => {
                        onChange('FAMILY');
                      }}
                    >
                      <View
                        className={cn(
                          'flex-row gap-2 items-center px-2 py-2 ',
                          {
                            ' bg-black-0 dark:bg-primary-900 rounded-[12px]':
                              value === 'FAMILY',
                          }
                        )}
                      >
                        <Image
                          // source={require('../../../../assets/images/plane.png')}
                          source={require('../../../assets/images/plane.png')}
                          contentFit="contain"
                          style={{ height: 12, width: 12 }}
                        />
                        <Text
                          className={cn(
                            'text-black-0 dark:text-black-300',
                            value === 'FAMILY'
                              ? 'dark:text-black-200'
                              : 'text-black-300'
                          )}
                        >
                          Family
                        </Text>
                      </View>
                    </Pressable>

                    <Pressable
                      onPress={() => {
                        onChange('GROUP');
                      }}
                    >
                      <View
                        className={cn(
                          'flex-row gap-2 items-center px-2 py-2 ',
                          {
                            'bg-blue dark:bg-primary-900 rounded-[12px] text-black-0':
                              value === 'GROUP',
                          }
                        )}
                      >
                        <Image
                          source={require('../../../assets/images/plane.png')}
                          contentFit="contain"
                          style={{ height: 12, width: 12 }}
                        />
                        <Text
                          className={cn(
                            'dark : text-black-300',
                            value === 'GROUP'
                              ? 'dark:text-black-200'
                              : 'text-black-300'
                          )}
                        >
                          Group
                        </Text>
                      </View>
                    </Pressable>
                  </View>
                )}
              />
            </InputLabelled>
            {errors.typeOfTraveller && (
              <Text className="text-red-500">
                {errors.typeOfTraveller.message?.toString()}
              </Text>
            )}

            {/* your gender */}
            <InputLabelled label="Your Gender?">
              <Controller
                control={control}
                name="gender"
                render={({ field: { value, onChange } }) => (
                  <View className="mt-2.5  flex-row justify-between  rounded-[12px] border border-black-100 border-borderdark bg-black-0 px-5 py-[14px] text-black-0 dark:bg-bgtextInput">
                    <Pressable onPress={() => onChange('MALE')}>
                      <View
                        className={cn(
                          'flex-row gap-2 items-center px-2 py-2 ',
                          {
                            ' bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
                              value === 'MALE',
                          }
                        )}
                      >
                        <Text
                          className={cn(
                            'text-black-0 dark:text-black-300',
                            value === 'MALE'
                              ? 'dark:text-black-200 '
                              : 'text-black-300'
                          )}
                        >
                          Male
                        </Text>
                      </View>
                    </Pressable>

                    <Pressable onPress={() => onChange('FEMALE')}>
                      <View
                        className={cn(
                          'flex-row gap-2 items-center px-2 py-2 ',
                          {
                            ' bg-black-0 dark:bg-primary-900 rounded-[12px]':
                              value === 'FEMALE',
                          }
                        )}
                      >
                        <Text
                          className={cn(
                            'text-black-0 dark:text-black-300',
                            value === 'FEMALE'
                              ? 'dark:text-black-200'
                              : 'text-black-300'
                          )}
                        >
                          Female
                        </Text>
                      </View>
                    </Pressable>
                    <Pressable onPress={() => onChange('OTHERS')}>
                      <View
                        className={cn(
                          'flex-row gap-2 items-center px-2 py-2 ',
                          {
                            'bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
                              value === 'OTHERS',
                          }
                        )}
                      >
                        <Text
                          className={cn(
                            'dark : text-black-300',
                            value === 'OTHERS'
                              ? 'dark:text-black-200'
                              : 'text-black-300'
                          )}
                        >
                          Others
                        </Text>
                      </View>
                    </Pressable>
                  </View>
                )}
              />
            </InputLabelled>

            {/* gender prefernce */}
            <View>
              <View className="flex-row items-center justify-between">
                <InputLabelled
                  label="Gender Preference"
                  className="flex-row items-center gap-4"
                >
                  <Controller
                    control={control}
                    name="openToAllGender"
                    render={({ field: { onChange, value } }) => (
                      <Switch
                        accessibilityLabel=""
                        label="Open for all genders"
                        className="text-black-300"
                        checked={value}
                        onChange={(checked) => {
                          onChange(checked);
                        }}
                      />
                    )}
                  />
                </InputLabelled>
              </View>
              {!watch('openToAllGender') && (
                <Controller
                  control={control}
                  name="genderPreference"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder="Enter gender preference"
                      value={value}
                      onChangeText={(text) => onChange(text.toUpperCase())}
                    />
                  )}
                />
              )}
              {errors.genderPreference && (
                <Text className="text-red-500">
                  {errors.genderPreference.message?.toString()}
                </Text>
              )}
            </View>

            {/* language traveller speaks */}
            <InputLabelled label="Language Traveller Speaks">
              <Controller
                control={control}
                name="languages"
                render={({ field: { value, onChange } }) => (
                  <Pressable
                    onPress={() => {
                      router.push('/form/companion-required/search');
                    }}
                  >
                    <View className="mt-2.5 flex-row flex-wrap items-center gap-2 rounded-[12px] border border-black-100 border-borderdark p-5 dark:bg-bgtextInput">
                      {languageThatTravellerSpeaks.length === 0 ? (
                        <Text className="text-[14px] text-black-300">
                          Select Language
                        </Text>
                      ) : (
                        languageThatTravellerSpeaks.map((language) => (
                          <Text
                            key={language.id}
                            className="font-inter text-[14px] text-black-0 dark:text-black-100"
                          >
                            {language.name}
                          </Text>
                        ))
                      )}
                    </View>
                  </Pressable>
                )}
              />
            </InputLabelled>
            {errors.languages && (
              <Text className="text-red-500">
                {errors.languages.message?.toString()}
              </Text>
            )}

            {/* write about you */}
            <InputLabelled label="Write about you">
              <Controller
                control={control}
                name="about"
                render={({ field: { onChange, value } }) => (
                  <InputText
                    placeholder="Write about yourself"
                    value={value}
                    onChangeText={onChange}
                  />
                )}
              />
            </InputLabelled>
            {errors.about && (
              <Text className="text-red-500">
                {errors.about.message?.toString()}
              </Text>
            )}
          </View>
        </ScrollView>
      </View>

      <View className="mb-8 px-5">
        <Button
          variant="secondary"
          label="Save Changes"
          loading={isLoading}
          onPress={handleSubmit(onSubmit, onError)}
        />
      </View>
      {renderModal()}
    </View>
  );
};

export default PersonalInfoPage;
