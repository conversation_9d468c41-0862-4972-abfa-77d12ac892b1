import Entypo from '@expo/vector-icons/Entypo';
import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Image, Modal, Pressable, StatusBar, Text, View } from 'react-native';
import { Query } from 'react-native-appwrite';

import InputText from '@/components/input-txt';
import { account, databases } from '@/lib/appwrite';

export const Data = [
  {
    id: 1,
    name: '<PERSON>',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '10.59pm',
    noofmsg: '2',
  },
  {
    id: 2,
    name: '<PERSON>',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '10.59pm',
    noofmsg: '2',
  },
  {
    id: 3,
    name: '<PERSON>',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '11 Jul',
    // noofmsg: '2',
  },
  {
    id: 4,
    name: 'Jason Allen',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '02 Jun',
    // noofmsg: '2',
  },
  {
    id: 5,
    name: 'Jason Allen',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '24 Mar',
    // noofmsg: '2',
  },
  {
    id: 6,
    name: 'Jason Allen',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '03 Jan',
    // noofmsg: '2',
  },
  {
    id: 7,
    name: 'Jason Allen',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '19 Dec 2023',
    // noofmsg: '2',
  },
  {
    id: 8,
    name: 'Jason Allen',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '19 Dec 2023',
    // noofmsg: '2',
  },
  {
    id: 9,
    name: 'Jason Allen',
    interConnectingFlight1: 'LAX',
    interConnectingFlight2: 'JFK',
    interConnectingFlight3: 'LHR',
    detail: 'It was really great to meet you, hope y..',
    timedate: '19 Dec 2023',
    // noofmsg: '2',
  },
];

export default function Chat() {
  const [openModal, setOpenModal] = useState(false);

  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="pt-safe flex-1 items-center justify-end  dark:bg-bgmodal">
          <View className="w-full rounded-t-lg bg-white px-5 dark:bg-primary-950">
            <View className="mb-4 py-10">
              <Pressable onPress={() => setOpenModal(false)}>
                <Image
                  source={require('../../../assets/images/cross.png')}
                  resizeMode="contain"
                  style={{ height: 24, width: 24 }}
                />
              </Pressable>
              <View className="my-6">
                <Pressable className="mb-5 flex-row items-center py-3">
                  <Image
                    source={require('../../../assets/images/select.png')}
                    className="mr-5 size-6"
                  />
                  <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                    Select All
                  </Text>
                </Pressable>
                <Pressable className="flex-row items-center justify-between py-3">
                  <View className=" flex-row items-center">
                    <Image
                      source={require('../../../assets/images/helpcenter.png')}
                      className="mr-5 size-6"
                    />
                    <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                      Help Center
                    </Text>
                  </View>
                  <Entypo name="chevron-right" size={20} color="#FDFDFD" />
                </Pressable>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  const getAllBookings = async () => {
    try {
      const user = await account.get();
      const bookings = await databases.listDocuments(
        '6711d07f002f03a35d08',
        '675aaf7a0015cbc51fd6',
        [Query.equal('userId', user.$id)]
      );
      console.log(
        'bookings are',
        bookings.documents.map((i) => i.bookingCompanion)
      );
    } catch (err) {
      console.log('error in getting booking are', err);
    }
  };

  useEffect(() => {
    //get booking with logged in user id
    getAllBookings();
  }, []);

  return (
    <View className="flex-1 justify-between bg-black-0 px-5 dark:bg-primary-950">
      <StatusBar barStyle={'light-content'} />
      {renderModal()}
      <View className="pt-safe flex-row items-center justify-between">
        <InputText
          placeholder="Search"
          placeholderTextColor={'#B3B3B3'}
          className="flex-1 dark:bg-primary-900 "
          // iconSourceFirst={require("../../../../assets/images/search-icon.png")}
          iconSourceFirst={require('../../../assets/images/search-icon.png')}
          iconStyleFirst={{ height: 14, width: 14 }}
        />
        <Pressable onPress={() => setOpenModal(true)}>
          <Image
            source={require('../../../assets/images/dot.png')}
            resizeMode="contain"
            className="ml-3 mt-2 size-6"
          />
        </Pressable>
      </View>

      {Data.length === 0 ? (
        // Render the ChatsEmpty component when there's no chats data available
        <View className="justify-between">
          {/* Empty State Image */}
          <View className="my-[67px] items-center">
            <Image
              source={require('../../../assets/images/pana.png')}
              resizeMode="contain"
              style={{ width: 279, height: 170 }}
            />
          </View>

          {/* Empty State Text */}
          <View className="items-center gap-3">
            <Text className="font-inter text-[16px] font-bold leading-6 dark:text-black-0">
              No chat available
            </Text>
            <Text className="text-center font-inter text-[16px] font-normal leading-6 dark:text-black-300">
              Find a match and start a conversation with your travel companion
              using the chat below
            </Text>
          </View>

          {/* Start Chat Button */}
          <View className="mt-16 w-full flex-row items-center justify-center">
            <Pressable
              className="rounded-[12px] border border-black-200 bg-black-950 px-6 py-3.5 dark:bg-primary-50"
              onPress={() => router.push('/welcome/')}
            >
              <Text className="font-inter text-[16px] font-normal leading-6 text-primary-950 dark:text-primary-950">
                Start Chat
              </Text>
            </Pressable>
          </View>

          {/* Advertisement Image */}
          <View className="flex-end mt-40 h-full">
            <Image
              source={require('../../../assets/images/ad.png')}
              className="h-24 w-full rounded-xl"
              resizeMode="cover"
            />
          </View>
        </View>
      ) : (
        <View className="flex-1">
          <FlashList
            showsVerticalScrollIndicator={false}
            data={Data}
            keyExtractor={(item) => item.id.toString()}
            estimatedItemSize={200}
            renderItem={({ item }) => (
              <View className="flex-1">
                {item.id === 3 ? (
                  <Text className="mb-4 mt-7 font-PoppinsSemiBold text-lg font-semibold text-black-200">
                    Previous Chats
                  </Text>
                ) : null}
                <Pressable
                  onPress={() => router.push('/chat-screens/')}
                  className="mb-2 flex-1 rounded-md bg-[#20202033] p-3"
                >
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      {/* profile image */}
                      <Image
                        source={require('../../../assets/images/profile.png')}
                        className="mr-3 size-10"
                      />
                      {/* center text part */}
                      <View>
                        <Text className="font-inter text-base font-medium text-black-50">
                          {item.name}
                        </Text>
                        <View className="flex-row items-center">
                          <Text className="font-inter text-xs font-medium text-black-300">
                            {item.interConnectingFlight1}
                          </Text>
                          <Image
                            source={require('../../../assets/images/flight.png')}
                            className="mx-0.5 size-2"
                          />
                          <Text className="font-inter text-xs font-medium text-black-300">
                            {item.interConnectingFlight2}
                          </Text>
                          <Image
                            source={require('../../../assets/images/flight.png')}
                            className="mx-0.5 size-2"
                          />
                          <Text className="font-inter text-xs font-medium text-black-300">
                            {item.interConnectingFlight3}
                          </Text>
                        </View>
                        <Text className="mt-1 font-inter text-xs font-normal text-black-50">
                          {item.detail}
                        </Text>
                      </View>
                    </View>

                    {/* right icons and notification part */}
                    <View>
                      <Text className="mb-3 font-inter text-xs font-medium text-black-500">
                        {item.timedate}
                      </Text>

                      {item.noofmsg && (
                        <View className=" self-end rounded-2xl bg-secondary-400 px-1">
                          <Text className="p-1 font-inter text-[10px] font-medium text-blue">
                            {item.noofmsg}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                </Pressable>
              </View>
            )}
          />
          <View className="mb-20" />
        </View>
      )}
    </View>
  );
}
