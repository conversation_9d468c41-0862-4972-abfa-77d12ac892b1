import { FlashList } from '@shopify/flash-list';
import React, { useEffect, useState } from 'react';
import { Image, ScrollView, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import SkeletonOffers from '@/components/skeleton-offers';
import { FocusAwareStatusBar } from '@/components/ui';

export default function Offers() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-primary-950">
        <FocusAwareStatusBar />
        <ScrollView className="mb-10" showsVerticalScrollIndicator={false}>
          {loading ? (
            <View className="flex-1 gap-5">
              <SkeletonOffers />
              <SkeletonOffers />
              <SkeletonOffers />
              <SkeletonOffers />
              <SkeletonOffers />
            </View>
          ) : (
            <FlashList
              data={Data}
              estimatedItemSize={50}
              renderItem={({ item }) => (
                <View className="flex-1 gap-5">
                  <Image
                    source={item.source}
                    resizeMode="contain"
                    style={{ height: 147, width: '100%' }}
                  />
                  <Image
                    source={require('../../../assets/images/banner2.png')}
                    resizeMode="contain"
                    style={{ height: 147, width: '100%' }}
                  />
                  <Image
                    source={require('../../../assets/images/banner3.png')}
                    resizeMode="contain"
                    style={{ height: 147, width: '100%' }}
                  />
                  <Image
                    source={require('../../../assets/images/banner4.png')}
                    resizeMode="contain"
                    style={{ height: 147, width: '100%' }}
                  />
                  <Image
                    source={require('../../../assets/images/banner4.png')}
                    resizeMode="contain"
                    style={{ height: 147, width: '100%' }}
                  />
                  <Image
                    source={require('../../../assets/images/banner4.png')}
                    resizeMode="contain"
                    style={{ height: 147, width: '100%' }}
                  />
                </View>
              )}
            />
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const Data = [
  {
    id: 1,
    source: require('../../../assets/images/banner1.png'),
  },
];
