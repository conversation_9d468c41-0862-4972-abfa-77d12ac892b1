import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Constants from 'expo-constants';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import * as Browser from 'expo-web-browser';
import React, { useEffect, useState } from 'react';
import {
  // StatusBar,
  Modal,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  // Dimensions,
} from 'react-native';

import { FocusAwareStatusBar } from '@/components/ui';
import { signOut } from '@/lib';
import { account } from '@/lib/appwrite';
import { getUserData } from '@/lib/user-data-service';
// const { height, width } = Dimensions.get('screen');
import { useLanguageStore } from '@/store/language-store';
import { UserType, userTypeStore } from '@/store/user-type-store';

export default function Profile() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [name, setName] = useState<string | null>();
  const [traveller, setTraveller] = useState<string>('');
  const [image, setImage] = useState<string | null>(null);
  const { userType, changeUserType } = userTypeStore((state) => {
    return {
      userType: state.userType,
      changeUserType: state.changeUserType,
    };
  });
  console.log('user type is', userType);

  async function userDetails() {
    const user = await account.get();
    setPhoneNumber(user.phone);
    setName(user.name);

    // Get user data from the database instead of preferences
    const userData = await getUserData();
    if (userData && userData.userProfileUrl) setImage(userData.userProfileUrl);

    // const userDetails = await databases.getDocument(
    //   '6711d07f002f03a35d08',
    //   '675aaac0000029cc416c',
    //   userType === UserType.TRAVELLER ? user.$id : `c-${user.$id}`
    // );
    // console.log('user details areeeee', userDetails);
    // setTraveller(userDetails.typeOfTraveller);
  }

  useEffect(() => {
    userDetails();
  }, []);
  // const { isDay, toggleTheme } = useThemeStore();
  // const animation = useSharedValue(isDay ? 0 : 22);

  // const animatedStyle = useAnimatedStyle(() => {
  //   return {
  //     transform: [{ translateX: animation.value }],
  //   };
  // });

  // const handleToggleTheme = () => {
  //   if (animation.value === 0) {
  //     animation.value = withTiming(22, { duration: 400 });
  //   } else {
  //     animation.value = withTiming(0, { duration: 400 });
  //   }
  //   toggleTheme();
  // };

  const [openModal, setOpenModal] = useState(false);
  const { emptyLanguage } = useLanguageStore((state) => {
    return {
      emptyLanguage: state.emptyLanguage,
    };
  });
  const handleLogout = async () => {
    try {
      // Delete the user account
      // await account.delete();

      // Delete the current session
      await account.deleteSession('current');

      // Sign out from the local state
      signOut();
      emptyLanguage();

      setOpenModal(false);
      router.push('/auth/login');
    } catch (error) {
      console.error('Error during logout', error);
      // Optionally show an error message to the user
    }
  };
  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="flex-1 items-center justify-center px-5 dark:bg-bgmodal ">
          <View
            // style={{ height: height * 0.165, width: width * 0.6279 }}
            className="rounded-[16px]  bg-white pt-6 dark:bg-blue"
          >
            <View className="w-3/4 items-center justify-center">
              <View className="mb-2.5 w-full items-center p-4 pt-2">
                <Text className="font-PoppinsSemiBold text-lg  font-semibold text-black-950  dark:text-charcoal-50">
                  Logout
                </Text>
                <Text className="text-wrap text-center font-inter text-xs font-normal leading-normal dark:text-black-100">
                  Logging out will end your current session.{'\n'}
                  Do you want to continue?
                </Text>
              </View>

              <View className="w-full flex-row items-center">
                <View className=" w-1/2 flex-1 items-center border-r border-t border-black-0 px-6 py-3">
                  <Pressable onPress={handleLogout}>
                    <Text className="font-PoppinsRegular text-base font-normal text-black-0 dark:text-black-300 ">
                      Yes, Logout
                    </Text>
                  </Pressable>
                </View>

                <View className="w-1/2 flex-1 items-center border-t border-black-0 px-6 py-3">
                  <Pressable
                    onPress={() => {
                      setOpenModal(false);
                    }}
                  >
                    <Text className="font-PoppinsBold text-base font-bold text-black-0 dark:text-black-50">
                      Cancel
                    </Text>
                  </Pressable>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const handleSwitchAccount = async () => {
    const user = await account.get();
    try {
      // Determine the target user type (opposite of current type)
      const targetUserType =
        userType === UserType.TRAVELLER
          ? UserType.COMPANION
          : UserType.TRAVELLER;
      console.log('target user is', targetUserType);
      // Check if account exists for the target user type
      // const targetUserAccount = await databases.listDocuments(
      //   '6711d07f002f03a35d08',
      //   '675aaac0000029cc416c',
      //   [
      //     Query.equal('typeOfUser', targetUserType),
      //     Query.equal('UID', user.$id),
      //   ]
      // );

      // if (targetUserAccount.documents.length == 0) {
      //   //we need to show the another page
      //   router.push('/switch-account/');
      //   console.log('we need to create the acount for target user');
      // } else {
      //   console.log(
      //     'we get the account for target user , switch to home screen and changes are made in profile'
      //   );
      changeUserType(
        userType === UserType.TRAVELLER
          ? UserType.COMPANION
          : UserType.TRAVELLER
      );
      // }
    } catch (err) {
      console.log('error isss ther', err);
    }
  };
  return (
    <View className="flex-1">
      <FocusAwareStatusBar />
      {/* Header */}
      <View className="pt-safe px-5 pb-6 dark:bg-primary-850 ">
        <View className="gap-2.5">
          <Text className="font-PoppinsBold text-[20px] font-bold leading-[30px] dark:text-black-0">
            Profile
          </Text>
          <Pressable
            onPress={() => {
              router.push('/personal-info');
            }}
          >
            <View className="w-full flex-row justify-between">
              <View className="flex-row items-center gap-3">
                <Image
                  source={
                    image ? image : require('../../../assets/images/user.png')
                  }
                  contentFit="cover"
                  className="rounded-full"
                  style={{ width: 60, height: 60 }}
                  onLoadStart={() => setIsLoading(true)}
                  onLoadEnd={() => setIsLoading(false)}
                />
                {/* {isLoading && image && (
                  <ActivityIndicator size="small" color="#000000" />
                )} */}
                <View className="gap-1 ">
                  <Text className="font-PoppinsSemiBold text-[18px] font-semibold leading-7 dark:text-black-100">
                    {name}
                  </Text>
                  <Text className="font-inter text-[14px] font-normal leading-5 dark:text-black-100">
                    {phoneNumber}
                  </Text>
                </View>
              </View>

              <View className="flex-col justify-between">
                <Image
                  source={require('../../../assets/images/edit.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 20 }}
                  className="self-end"
                />
                {traveller && (
                  <View className="flex-row items-center rounded-lg bg-primary-900 p-2">
                    <Image
                      source={require('../../../assets/images/airplane.png')}
                      className="size-3"
                    />
                    <Text className="font-inter text-xs font-medium text-black-200">
                      {traveller} Traveller
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </Pressable>
        </View>
      </View>
      <View className="flex-1 bg-primary-900 px-5 ">
        <ScrollView
          className=" mb-8 flex-1"
          showsVerticalScrollIndicator={false}
        >
          <View className="mb-5 gap-5">
            {/* I will have to add the bg-color of this using linear gradient */}
            <Pressable
              className="mt-10 flex-row items-center justify-between rounded-2xl bg-primary-850 p-3.5"
              onPress={() => {
                router.push('/switch-account/');
              }}
            >
              <View className="gap-1">
                <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 text-black-0">
                  Become a Thedal Companion
                </Text>
                <Text className="font-inter text-[14px] font-medium leading-5 text-black-300">
                  It Pays to Travel Together!
                </Text>
              </View>
              <Image
                source={require('../../../assets/images/companion.svg')}
                contentFit="contain"
                style={{ height: 60, width: 60 }}
              />
            </Pressable>

            {/* Container2 it will contain all the settings */}
            <View className="gap-2.5 rounded-[12px] bg-primary-950 p-3">
              <Text className="font-PoppinsSemiBold text-[18px] font-semibold leading-7  dark:text-black-50">
                Settings
              </Text>

              {/* <View className="flex-row items-center justify-between py-3">
                <View className="flex-row items-center gap-5">
                  <Image
                    source={require('../../../assets/images/changemode.svg')}
                    contentFit="contain"
                    style={{ height: 24, width: 24 }}
                  />
                  <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                    Dark Mode
                  </Text>
                </View>

                <Pressable
                  className={cn(
                    'w-[45px] rounded-full px-1 py-1 flex-row items-center ',
                    isDay ? 'bg-graybg' : 'bg-black-50'
                  )}
                  onPress={handleToggleTheme}
                >
                  <Animated.View
                    style={[
                      {
                        padding: 2,
                        borderRadius: 100,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#1f2937',
                      },
                      animatedStyle,
                    ]}
                  >
                    <Image
                      source={
                        isDay
                          ? require('../../../assets/images/sun.png')
                          : require('../../../assets/images/moon.svg')
                      }
                      contentFit="contain"
                      style={{ height: 12, width: 12 }}
                    />
                  </Animated.View>
                </Pressable>
              </View> */}

              {/* <Pressable
                onPress={() => {
                  router.push('/transactions');
                }}
              >
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/transaction1.png')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Transaction
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable> */}

              {userType === UserType.COMPANION ? (
                <Pressable
                  onPress={() => {
                    router.push('/wallet/connect-account');
                  }}
                >
                  <View className="flex-row items-center justify-between py-3">
                    <View className="flex-row items-center gap-5">
                      <Image
                        source={require('../../../assets/images/wallet.svg')}
                        contentFit="contain"
                        style={{ height: 24, width: 24 }}
                      />
                      <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                        Manage Payouts
                      </Text>
                    </View>
                    <Image
                      source={require('../../../assets/images/right-back.svg')}
                      contentFit="contain"
                      style={{ height: 20, width: 20 }}
                    />
                  </View>
                </Pressable>
              ) : (
                <Pressable
                  onPress={() => {
                    router.push('/wallet');
                  }}
                >
                  <View className="flex-row items-center justify-between py-3">
                    <View className="flex-row items-center gap-5">
                      <Image
                        source={require('../../../assets/images/wallet.svg')}
                        contentFit="contain"
                        style={{ height: 24, width: 24 }}
                      />
                      <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                        Wallet
                      </Text>
                    </View>
                    <Image
                      source={require('../../../assets/images/right-back.svg')}
                      contentFit="contain"
                      style={{ height: 20, width: 20 }}
                    />
                  </View>
                </Pressable>
              )}

              <Pressable
                onPress={() => {
                  router.push('/bookings');
                }}
              >
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/calender.svg')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Bookings
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable>
              <Pressable
                onPress={() => {
                  router.push('/leader-board');
                }}
              >
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/leadership.svg')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Leaderboard
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable>
              {/* <Pressable
                onPress={() => {
                  router.push('/settings');
                }}
              >
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/settings.svg')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Setting
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable> */}
            </View>

            {/* Container 3 contain the companion account */}
            <View className="gap-2.5 rounded-[12px] bg-primary-950 p-3">
              <Text className="font-PoppinsSemiBold text-[18px] font-semibold leading-7 dark:text-black-50">
                {userType === UserType.COMPANION ? 'Traveller' : 'Companion'}
              </Text>
              <TouchableOpacity onPress={handleSwitchAccount}>
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/settings.svg')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />

                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Switch Account as{' '}
                      {userType === UserType.TRAVELLER
                        ? 'companion'
                        : 'traveller'}
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </TouchableOpacity>
            </View>

            {/* Container 4 it will hold Referrals & Feedback */}
            <Pressable onPress={() => {}}>
              <View className="gap-2.5 rounded-[12px] bg-primary-950 p-3">
                <Text className="font-PoppinsSemiBold text-[18px] font-semibold leading-7 dark:text-black-50">
                  Referrals & Feedback
                </Text>
                <Pressable
                  onPress={() => {
                    router.push('/refer-to');
                  }}
                >
                  <View className="flex-row items-center justify-between py-3">
                    <View className="flex-row items-center gap-5">
                      <Image
                        source={require('../../../assets/images/chat.svg')}
                        contentFit="contain"
                        style={{ height: 24, width: 24 }}
                      />
                      <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                        Refer Your Friend
                      </Text>
                    </View>
                    <Image
                      source={require('../../../assets/images/right-back.svg')}
                      contentFit="contain"
                      style={{ height: 20, width: 20 }}
                    />
                  </View>
                </Pressable>

                <Pressable
                  onPress={() => {
                    router.push('/feedback');
                  }}
                >
                  <View className="flex-row items-center justify-between py-3">
                    <View className="flex-row items-center gap-5">
                      <Image
                        source={require('../../../assets/images/chat.svg')}
                        contentFit="contain"
                        style={{ height: 24, width: 24 }}
                      />
                      <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                        Give us Feedback
                      </Text>
                    </View>
                    <Image
                      source={require('../../../assets/images/right-back.svg')}
                      contentFit="contain"
                      style={{ height: 20, width: 20 }}
                    />
                  </View>
                </Pressable>
              </View>
            </Pressable>
            {/* contaner 5 Terms & Conditions */}
            <View className="gap-2.5 rounded-[12px] bg-primary-950 p-3">
              <Text className="font-PoppinsSemiBold text-[18px] font-semibold leading-7 dark:text-black-50">
                Terms & Conditions
              </Text>
              <Pressable
                onPress={() => {
                  Browser.openBrowserAsync('https://thedalapp.com/privacy');
                }}
              >
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/settings.svg')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Privacy Policy
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable>
              <Pressable
                onPress={() => {
                  Browser.openBrowserAsync('https://thedalapp.com/terms');
                }}
              >
                <View className="flex-row items-center justify-between py-3">
                  <View className="flex-row items-center gap-5">
                    <Image
                      source={require('../../../assets/images/settings.svg')}
                      contentFit="contain"
                      style={{ height: 24, width: 24 }}
                    />
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                      Terms of Services
                    </Text>
                  </View>
                  <Image
                    source={require('../../../assets/images/right-back.svg')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable>
            </View>

            <Pressable
              onPress={() => {
                setOpenModal(true);
              }}
              className="flex-row items-center gap-2 self-center"
            >
              <MaterialIcons name="logout" size={20} color="red" />
              <Text className="font-Poppins text-center text-[16px] dark:text-danger-50">
                Log Out
              </Text>
            </Pressable>

            <View className="mt-2 flex-row items-center justify-center">
              <Text className="font-inter text-[14px] text-gray-500 dark:text-gray-400">
                App Version: {Constants.expoConfig?.version || '0.0.1'}
              </Text>
            </View>

            <View className="mb-4 mt-2 flex-row items-center justify-center">
              <Text className="font-inter text-[14px] text-gray-500 dark:text-gray-400">
                Developed by{' '}
              </Text>
              <Pressable
                onPress={() =>
                  Browser.openBrowserAsync('https://nextflytech.com/')
                }
              >
                <Text className="font-inter text-[14px] font-medium text-white">
                  Nextfly Technologies
                </Text>
              </Pressable>
            </View>
          </View>
        </ScrollView>
      </View>
      {renderModal()}
      <View className="mb-12" />
    </View>
  );
}
