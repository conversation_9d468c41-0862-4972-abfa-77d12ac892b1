import { Env } from '@env';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Pressable,
  ScrollView,
  StatusBar,
  Text,
  View,
} from 'react-native';
import { Query } from 'react-native-appwrite';

import BookingsCardNoCompanion from '@/components/bookings-card-no-companion';
import CompanionDetailCard from '@/components/companion-detailCard';
import { Button } from '@/components/ui';
import { account, databases } from '@/lib/appwrite';

const images = [
  require('../../../assets/images/banner1.png'),
  require('../../../assets/images/banner2.png'),
  require('../../../assets/images/banner3.png'),
  require('../../../assets/images/banner1.png'),
  require('../../../assets/images/banner3.png'),
];

// type ITravelerFlightDates = {
//   FlightCompany: string;
//   FlightPNR: string;
//   FlightDate: string;
//   FlightTime: string;
// };

// const zodValidation = z.object({
//   FlightCompany: z
//     .string({ required_error: 'FlightCompany required' })
//     .min(1, 'Required')
//     .max(10, 'FlightCompany is too long'),
//   FlightPNR: z.string({ required_error: 'Required' }).min(1, 'Required'),
//   FlightDate: z.string({
//     required_error: 'FlightDate required',
//     invalid_type_error: 'Invalid date format',
//   }),
//   FlightTime: z
//     .string({ required_error: 'FlightTime required' })
//     .min(1, 'Required'),
// });

export default function HomePage() {
  console.log('HomePage in tabs');
  const [currentPage, setCurrentPage] = useState(0);
  const [bookings, setBookings] = useState<any[]>([]);
  // const { width } = useWindowDimensions();
  // const [date, setDate] = useState(new Date());
  // const [time, setTime] = useState(new Date());
  // const [showDatePicker, setShowDatePicker] = useState(false);
  // const [showTimePicker, setShowTimePicker] = useState(false);
  // const [swapped, setSwapped] = useState(false);
  const [name, setName] = useState<string | null>();
  useEffect(() => {
    (async () => {
      const user = await account.get();
      setName(user.name);
    })();
  }, []);
  // const toggleSwapp = () => {
  //   setSwapped(!swapped);
  // };

  // const toggleDatepicker = () => {
  //   setShowDatePicker(true);
  // };

  // const toggleTimepicker = () => {
  //   setShowTimePicker(true);
  // };

  // const onDateChange = (
  //   event: DateTimePickerEvent,
  //   selectedDate: Date | undefined
  // ) => {
  //   const currentDate = selectedDate || date;
  //   setShowDatePicker(Platform.OS !== 'android');
  //   setDate(currentDate);

  //   const formattedDate = currentDate.toDateString();
  //   console.log('Selected Date:', formattedDate);
  //   setValue('FlightDate', formattedDate, { shouldValidate: true });
  // };

  // const onTimeChange = (
  //   event: DateTimePickerEvent,
  //   selectedTime: Date | undefined
  // ) => {
  //   const currentTime = selectedTime || time;
  //   setShowTimePicker(Platform.OS !== 'android');
  //   setTime(currentTime);

  //   const formattedTime = currentTime.toTimeString().split(' ')[0];
  //   console.log('Selected Time:', formattedTime); // Add this line
  //   setValue('FlightTime', formattedTime, { shouldValidate: true });
  // };

  // const toggle = () => {
  //   // setSelected(!selected);
  // };

  // const { control, handleSubmit, setValue, getValues } =
  //   useForm<ITravelerFlightDates>({
  //     resolver: zodResolver(zodValidation),
  //     defaultValues: {
  //       FlightCompany: '',
  //       FlightPNR: '',
  //       FlightDate: '',
  //       FlightTime: '',
  //     },
  //   });

  // const { errors } = useFormState({ control });

  // const handleScroll = (event) => {
  //   const contentOffsetX = event.nativeEvent.contentOffset.x;
  //   const newPage = Math.floor(contentOffsetX / width);
  //   setCurrentPage(newPage);
  // };

  const handleScroll = (event: {
    nativeEvent: { contentOffset: { x: any } };
  }) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const pageWidth = 303 + 8; // Image width + marginHorizontal
    const newPage = Math.floor((contentOffsetX + pageWidth / 2) / pageWidth);
    setCurrentPage(newPage);
  };

  const renderItem = ({ item }: any) => (
    <Image
      source={item}
      style={{ width: 303, height: 114.289, marginHorizontal: 4 }}
      contentFit="contain"
    />
  );

  const getBookingFlightDetails = async () => {
    try {
      // const user = await account.get();
      const bookings = await databases.listDocuments(
        Env.COLLECTION_ID,
        Env.COLLECTION_BOOKING_FLIGHT_DETAILS,
        [
          // [Query.equal('userId', user.$id)]
          Query.or([
            Query.equal('status', 'ACTIVE'),
            Query.equal('status', 'PENDING'),
          ]),
          Query.orderDesc('$createdAt'),
          // Query.greaterThan('flightTime', new Date().toISOString()),
        ]
      );
      setBookings(bookings.documents);
      console.log('bookings are', JSON.stringify(bookings));
    } catch (err) {
      console.log('error in getting booking are', err);
    }
  };

  useEffect(() => {
    console.log('getttttting Flight Details');
    getBookingFlightDetails();
  }, []);

  return (
    <View className="w-full flex-1">
      <StatusBar barStyle={'dark-content'} />
      {/* <FocusAwareStatusBar /> */}

      <View className="w-full bg-black-0 px-5  py-2.5 ">
        <View className="pt-safe flex-row items-center justify-between px-2.5 py-2">
          {name && (
            <View className=" flex-row items-center gap-2">
              <Text className="font-PoppinsRegular text-[16px] font-normal leading-6 dark:text-blue">
                Welcome,
              </Text>
              <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 dark:text-blue">
                {name}
              </Text>
              <Image
                // source={require('../../../assets/images/handgif.png')}
                contentFit="contain"
                source={require('../../../assets/images/hand.gif')}
                // contentFit="contain"
                style={{ height: 30, width: 30 }}
              />
            </View>
          )}
          <Pressable
            onPress={() => {
              router.push('/notifications');
            }}
            className="rounded-[6px] bg-green p-[6.86px]"
          >
            <Image
              source={require('../../../assets/images/bell.svg')}
              // contentFit="contain"
              style={{ width: 18.286, height: 18.286 }}
              tintColor={'#F2F2F2'}
            />
          </Pressable>
        </View>
      </View>
      <ScrollView
        className="mb-20 flex-1 bg-black-0"
        showsVerticalScrollIndicator={false}
      >
        <View className="flex-1 bg-black-0">
          {/* <View className="py-2.5">
            <Text className="px-5 font-PoppinsBold text-[20px] font-bold leading-[30px] dark:text-blue">
              Fill up the details to find your companion to start your journey
            </Text>
          </View> */}

          <View className="flex-1 gap-6 rounded-t-[24px] bg-primary-950 px-5 py-6">
            <View className="flex flex-col gap-2">
              {bookings.map((booking) => (
                <BookingsCardNoCompanion
                  bookingId={booking.$id}
                  key={booking.$id}
                />
              ))}
            </View>
            {/* <View className="gap-2">
              <Pressable
                onPress={() => {
                  router.push('/form/companion-required/destination-search');
                }}
              >
                <View className="mt-2 flex-row items-center gap-2 rounded-t-[12px] border-x border-t border-customborder px-5 py-[14px] dark:bg-bgtextInput">
                  <Image
                    source={require('../../../assets/images/location.png')}
                    contentFit="contain"
                    style={{ width: 12, height: 15 }}
                  />
                  <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-300">
                    {swapped ? 'From where?' : 'Where to?'}
                  </Text>
                </View>

                <View className="flex-row items-center gap-2 rounded-b-[12px] border border-customborder px-5 py-[14px] text-[14px] font-normal leading-5 dark:bg-bgtextInput">
                  <Image
                    source={require('../../../assets/images/location.png')}
                    contentFit="contain"
                    style={{ width: 12, height: 15 }}
                  />
                  <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-300">
                    {swapped ? 'Where to?' : 'From Where?'}
                  </Text>
                </View>
              </Pressable>
              <Pressable onPress={toggleSwapp}>
                <View className="absolute bottom-10 right-0 rounded-[8px] border border-customborder bg-black-0 p-[6px] dark:bg-primary-950">
                  <Image
                    source={require('../../../assets/images/swaparrow.png')}
                    contentFit="contain"
                    style={{ height: 20, width: 20 }}
                  />
                </View>
              </Pressable>
              <View>
                <InputText
                  onPress={() => {}}
                  placeholder="Flight PNR/Ref/Confirmation Number"
                  iconSourceFirst={require('../../../assets/images/user-text-input.svg')}
                  iconStyleFirst={{ height: 15.981, width: 13.333 }}
                  placeholderTextColor={'#B3B3B3'}
                />
              </View>
              <View className="w-full flex-1 flex-row items-center gap-3">
                <Controller
                  name="FlightDate"
                  control={control}
                  render={({ field, fieldState }) => (
                    <InputText
                      value={field.value}
                      editable={false}
                      onChangeText={(num) => field.onChange(num)}
                      onPress={toggleDatepicker}
                      placeholder="Date"
                      iconSourceFirst={require('../../../assets/images/calender.svg')}
                      iconStyleFirst={{ height: 15, width: 15 }}
                      placeholderTextColor={'#B3B3B3'}
                      className="flex-1"
                    />
                  )}
                />

                <Controller
                  name="FlightTime"
                  control={control}
                  render={({ field, fieldState }) => (
                    <InputText
                      onPress={toggleTimepicker}
                      value={field.value}
                      onChangeText={(num) => field.onChange(num)}
                      editable={false}
                      placeholder="Time"
                      iconSourceFirst={require('../../../assets/images/watch.png')}
                      iconStyleFirst={{ height: 15.981, width: 13.333 }}
                      placeholderTextColor={'#B3B3B3'}
                      className="flex-1"
                    />
                  )}
                />
              </View>
            </View> */}

            <View>
              <Button
                variant="secondary"
                label="Find Companion"
                onPress={() => {
                  router.push('/form/companion-required/traveler-details');
                }}
              />
            </View>

            <View className="gap-2.5">
              <FlatList
                data={images}
                keyExtractor={(item, index) => index.toString()}
                horizontal
                showsHorizontalScrollIndicator={false}
                pagingEnabled
                onScroll={handleScroll}
                renderItem={renderItem}
              />
              <View className="flex-row justify-center">
                {images.map((_, index) => (
                  <View
                    key={index}
                    style={{
                      width: 6,
                      height: 6,
                      borderRadius: 4,
                      backgroundColor:
                        currentPage === index ? '#FDFDFD' : '#081D5E',
                      marginHorizontal: 4,
                    }}
                  />
                ))}
              </View>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 dark:text-black-50">
                Recommended List
              </Text>
              <View className="flex-row items-center gap-3">
                <Image
                  source={require('../../../assets/images/search-icon.png')}
                  contentFit="contain"
                  style={{ width: 18, height: 18 }}
                />
                <Image
                  source={require('../../../assets/images/sort-by-icon.svg')}
                  contentFit="contain"
                  style={{ width: 18, height: 15 }}
                />
              </View>
            </View>

            <CompanionDetailCard
              rating=""
              variant="default"
              title="Kaushiki"
              subTitle="Male, 53yrs, India"
            />
            <CompanionDetailCard
              rating=""
              variant="secondary"
              title="Kaushiki"
              subTitle="Male, 53yrs, India"
            />
          </View>
        </View>

        {/* {showDatePicker && (
          <DateTimePicker
            value={date}
            mode="date"
            display="default"
            onChange={onDateChange}
          />
        )}

        {showTimePicker && (
          <DateTimePicker
            value={time}
            mode="time"
            display="default"
            onChange={onTimeChange}
          />
        )} */}
      </ScrollView>
    </View>
  );
}
