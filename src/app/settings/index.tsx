import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';

import BackButton from '@/components/back-button';
import { Switch } from '@/components/ui';
import { FocusAwareStatusBar } from '@/components/ui';
import { getUserData, updateUserData } from '@/lib/user-data-service';

function SettingsPage() {
  const [notification, setNotification] = useState<boolean>(false);
  const [muteChat, SetMuteChat] = useState<boolean>(false);
  const [openMicroPhone, setOpenMicroPhone] = useState<boolean>(false);

  const [userData, setUserData] = useState<Record<string, any> | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const result = await getUserData();

        setNotification(result?.notification === true);
        SetMuteChat(result?.muteChat === true);
        setOpenMicroPhone(result?.openMicroPhone === true);

        setUserData(result);
      } catch (error) {
        console.error('Error fetching user data:', error);

        setNotification(false);
        SetMuteChat(false);
        setOpenMicroPhone(false);

        setUserData({});
      }
    };

    fetchUserData();
  }, []);

  const updateUserPreferences = async () => {
    try {
      const updatedData = {
        notification,
        muteChat,
        openMicroPhone,
      };

      await updateUserData(updatedData);
    } catch (error) {
      console.error('Error updating user data:', error);
    }
  };

  useEffect(() => {
    if (userData !== null) {
      updateUserPreferences();
    }
  }, [notification, muteChat, openMicroPhone]);

  return (
    <View className="flex-1 gap-6 bg-primary-950 px-5">
      <FocusAwareStatusBar />
      <BackButton
        text="Back"
        text2="Settings"
        onPress={() => {
          router.back();
        }}
      />
      {/* MainContainer */}
      <View className="gap-2.5">
        <View className="gap-3 rounded-xl border border-primary-950 p-3  ">
          <Text className="font-Poppins text-lg dark:text-black-50">
            General Settings
          </Text>
          {/* Container1 */}
          <View className="flex-row flex-wrap items-center gap-5 ">
            <Image
              source={require('../../../assets/images/notification.svg')}
              contentFit="contain"
              style={{ height: 24, width: 24 }}
            />
            <View className="flex-1 gap-1 ">
              <Text className="font-inter text-base font-medium text-black-950 dark:text-black-100">
                Notification
              </Text>
              <Text className="font-inter text-xs font-normal text-black-950 dark:text-black-300">
                Toggle on to mute the notification, you will not receive any
                notification related to app
              </Text>
            </View>
            <Switch
              accessibilityLabel=""
              className="text-black-300 "
              checked={notification}
              onChange={(checked) => setNotification(checked)}
            />
          </View>
        </View>

        {/* MainContainer2 */}

        <View className="gap-3 rounded-xl border border-primary-950 p-3  ">
          <Text className="font-Poppins text-lg dark:text-black-50">
            Chat Settings
          </Text>
          {/* Container1 */}
          <View className="flex-row flex-wrap items-center gap-5 ">
            <Image
              source={require('../../../assets/images/notification.svg')}
              contentFit="contain"
              style={{ height: 24, width: 24 }}
            />
            <View className="flex-1 gap-1 ">
              <Text className="font-inter text-base font-medium text-black-950 dark:text-black-100">
                Notification
              </Text>
              <Text className="font-inter text-xs font-normal text-black-950 dark:text-black-300">
                Mute companion chat notification on home screen
              </Text>
            </View>
            <Switch
              accessibilityLabel=""
              className="text-black-300 "
              checked={muteChat}
              onChange={(checked) => SetMuteChat(checked)}
            />
          </View>
          {/* Container2 */}
          <View className="flex-row flex-wrap items-center gap-5 ">
            <Image
              source={require('../../../assets/images/settings-mircrophone.svg')}
              contentFit="contain"
              style={{ height: 24, width: 24 }}
            />
            <View className="flex-1 gap-1 ">
              <Text className="font-inter text-base font-medium text-black-950 dark:text-black-100">
                Microphone access
              </Text>
              <Text className="font-inter text-xs font-normal text-black-950 dark:text-black-300">
                Slide in to give access to microphone while chatting
              </Text>
            </View>
            <Switch
              accessibilityLabel=""
              className="text-black-300 "
              checked={openMicroPhone}
              onChange={(checked) => setOpenMicroPhone(checked)}
            />
          </View>
        </View>
      </View>
    </View>
  );
}

export default SettingsPage;
