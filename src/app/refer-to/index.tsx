import * as Clipboard from 'expo-clipboard';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Pressable, Share, Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';
import { account } from '@/lib/appwrite';

const ReferToPage = () => {
  const [userId, setUserId] = useState<string>();

  const handleInvite = () => {
    console.log('invite');
    Share.share({
      message:
        'Check out Thedal app a travel companion app https://thedalapp.com?ref=' +
        userId,
    });
  };

  const getUser = async () => {
    const user = await account.get();
    console.log('user', user);
    setUserId(user.$id);
  };

  const copyToClipboard = async () => {
    if (userId) {
      await Clipboard.setStringAsync(userId);
      showMessage({
        message: 'Copied to clipboard',
        type: 'success',
      });
    }
  };

  useEffect(() => {
    getUser();
  }, []);

  return (
    <View className="flex-1">
      <View className="bg-darkcardbg px-5 py-3">
        <BackButton
          text="Back"
          text2="Refer to a friend"
          onPress={() => {
            router.back();
          }}
        />
      </View>
      {/* Section1 */}
      <View className="flex-1 gap-8 px-5 dark:bg-primary-950">
        <View className="mt-8">
          {/* part1 */}
          <View className="gap-3">
            <View className="gap-2">
              <Text className="text-center font-PoppinsMedium text-sm font-medium leading-5 dark:text-black-300">
                Invite Companion
              </Text>
              <Text className="text-center font-inter text-xl font-bold leading-7 dark:text-black-50">
                Invite your friends
              </Text>
            </View>
            {/* part 2 */}
            <View className="gap-6">
              {/* <View className="flex-row items-center gap-2  ">
                <Image
                  source={require('../../../assets/images/coines.png')}
                  contentFit="contain"
                  style={{ width: 50, height: 50 }}
                />
                <View className="flex-row">
                  <Text className="font-PoppinsBold text-4xl font-bold leading-[34px] dark:text-secondary-750">
                    50
                  </Text>
                  <Text className="self-end font-PoppinsMedium text-lg font-medium leading-7 dark:text-secondary-750 ">
                    coins
                  </Text>
                </View>
                <Text className="flex-1 text-wrap font-inter text-sm font-normal leading-5 dark:text-black-300">
                  For every companion or traveler who create free acco
                </Text>
              </View> */}
              {/* part3 */}

              <Image
                source={require('../../../assets/images/cuate.png')}
                contentFit="contain"
                style={{ width: '100%', height: 222 }}
              />
              <Pressable onPress={copyToClipboard}>
                <View className="flex-row gap-1 self-center rounded-2xl px-[18px] py-2.5 dark:bg-primary-900">
                  <Text className="font-inter text-sm font-medium leading-4 dark:text-black-50">
                    Invite code : {userId}
                  </Text>

                  <Image
                    source={require('../../../assets/images/frame.svg')}
                    contentFit="contain"
                    style={{ height: 12, width: 12 }}
                  />
                </View>
              </Pressable>
            </View>
          </View>
        </View>

        <View className="w-full flex-row items-center gap-8 ">
          {/* <View className=" w-1/2 flex-row items-center justify-center gap-2 rounded-xl border border-black-200 px-6 py-3.5 dark:bg-black-0">
            <Image
              source={require('../../../assets/images/whatapp-icon.svg')}
              contentFit="contain"
              style={{ height: 20, width: 20 }}
            />
            <Text className="font-inter text-sm font-medium leading-5 dark:text-blue">
              Invite{' '}
            </Text>
          </View> */}
          <Button
            variant="outline"
            label="Invite"
            leftIcon={
              <Image
                source={require('../../../assets/images/share.png')}
                contentFit="contain"
                style={{ height: 20, width: 20 }}
              />
            }
            onPress={handleInvite}
          />

          {/* <View className=" w-1/2 flex-row items-center justify-center   gap-2 rounded-xl px-6 py-3.5  dark:bg-[rgba(32,32,32,0.20)]">
            <Image
              source={require('../../../assets/images/share.png')}
              contentFit="contain"
              style={{ height: 20, width: 20 }}
            />
            <Text className="font-inter text-sm font-medium leading-5 dark:text-black-50">
              More Options{' '}
            </Text>
          </View> */}
        </View>
      </View>
    </View>
  );
};

export default ReferToPage;
