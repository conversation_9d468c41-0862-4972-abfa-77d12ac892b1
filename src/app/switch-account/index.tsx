import { Env } from '@env';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import {
  Dimensions,
  ImageBackground,
  Pressable,
  Text,
  View,
} from 'react-native';

import { Button } from '@/components/ui';
import { account, databases } from '@/lib/appwrite';
import { useLanguageStore } from '@/store/language-store';
import { UserType, userTypeStore } from '@/store/user-type-store';
const { height, width } = Dimensions.get('screen');

const SwitchAccountPage = () => {
  const { userType } = userTypeStore((state) => {
    return {
      userType: state.userType,
    };
  });

  const { addLanguage, languageThatTravellerSpeaks } = useLanguageStore(
    (state) => {
      return {
        addLanguage: state.addLanguage,
        languageThatTravellerSpeaks: state.languageThatTravellerSpeaks,
      };
    }
  );

  const createTargetUserAccount = async () => {
    const user = await account.get();

    const targetUserType =
      userType === UserType.TRAVELLER ? UserType.COMPANION : UserType.TRAVELLER;
    console.log('ceating the account for ', targetUserType);

    const documentId =
      userType === UserType.TRAVELLER ? user.$id : `c-${user.$id}`;
    console.log('Fetching existing user details with document ID:', documentId);
    // details h use user ki jiski profile bni hui h jaise abhi traveller ki bni hui h

    const loggedInUserDetails = await databases.getDocument(
      Env.COLLECTION_ID,
      Env.COLLECTION_BOOKING_COMPANION,
      documentId
      //   [Query.equal('UID', user.$id), Query.equal('typeOfUser', userType)],
    );
    console.log('loggedInuserDetails are', loggedInUserDetails);

    if (
      loggedInUserDetails.languages &&
      Array.isArray(loggedInUserDetails.languages)
    ) {
      loggedInUserDetails.languages.forEach((lang) => {
        addLanguage(lang.$id, lang.name);
      });
    }

    //setting up the targetUserData
    const targetUserData = {
      name: loggedInUserDetails.name,
      typeOfTraveller: loggedInUserDetails.typeOfTraveller,
      gender: loggedInUserDetails.gender,
      openToAllGender: loggedInUserDetails.openToAllGender,
      genderPreference: loggedInUserDetails.genderPreference,
      about: loggedInUserDetails.about,
      UID: user.$id,
      languages: languageThatTravellerSpeaks.map((i) => i.id) || [],
    };

    //creating the targetUser if it does not exists
    try {
      await databases.createDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_BOOKING_COMPANION,
        user.$id,
        targetUserData
      );
    } catch (err) {
      await databases.updateDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_BOOKING_COMPANION,
        user.$id,
        targetUserData
      );

      console.log('error in creating the details for targetUser', err);
    }
    router.push('/(app)/home');
  };

  return (
    <View className="flex-1">
      <ImageBackground
        source={require('../../../assets/images/globalmap.png')}
        className="py-safe"
        style={{
          height: height * 1,
          width: width * 1,
        }}
      >
        <View className="flex-1 dark:bg-primary-950">
          {/* Section1 Image */}
          <View>
            <Pressable
              className="flex-row items-center gap-2"
              onPress={() => {
                router.back();
              }}
            >
              <Image
                source={require('../../../assets/images/back-icon.svg')}
                contentFit="contain"
                style={{ height: 12, width: 6 }}
              />
              <Text className="font-inter text-sm font-medium dark:text-black-50 ">
                Back
              </Text>
            </Pressable>
            <Image
              source={require('/assets/images/onboarding2.png')}
              contentFit="cover"
              style={{
                borderRadius: 32,
                width: width * 1,
                height: height * 0.532,
              }}
            />
          </View>
          {/* Section 2 */}
          <View className="gap-7 px-5 pb-9 pt-6">
            <Text className="font-PoppinsBold text-xl font-bold dark:text-black-200">
              Become a Companion on Thedal
            </Text>

            <View>
              <View className="mb-2 flex-row flex-wrap items-center">
                <Text className="font-PoppinsSemiBold text-base font-semibold dark:text-black-100">
                  Earn Extra Income:
                </Text>
                <Text className="font-inter text-base dark:text-black-300">
                  {' '}
                  Receive compensation for your time and companionship.
                </Text>
              </View>
              <View className="flex-row flex-wrap items-center">
                <Text className="font-PoppinsSemiBold text-base font-semibold dark:text-black-100">
                  Enhance Your Social Skills:
                </Text>
                <Text className="font-inter text-base dark:text-black-300">
                  {' '}
                  Develop your communication and interpersonal skills.
                </Text>
              </View>
            </View>

            <Button
              variant="secondary"
              label="Create account as Companion"
              onPress={() => createTargetUserAccount()}
            />
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};

export default SwitchAccountPage;
