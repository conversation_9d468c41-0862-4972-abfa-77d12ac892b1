import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native';

import PastBooking from './past-booking';
import UpComingBooking from './upcoming-booking';

const Tab = createMaterialTopTabNavigator();

const initialLayout = {
  width: Dimensions.get('window').width,
};

interface TabLabelProps {
  focused: boolean;
  title: string;
}

const TabLabel: React.FC<TabLabelProps> = ({ focused, title }) => {
  return (
    <Text style={focused ? styles.activeLabel : styles.inactiveLabel}>
      {title}
    </Text>
  );
};

const CustomTabBarIndicator = ({ layout, navigation }: any) => {
  const activeIndex = navigation.getState().index;
  const tabWidth = layout.width / 2;
  const indicatorPercentage = 0.6;
  const indicatorWidth = tabWidth * indicatorPercentage;

  const leftPosition = activeIndex * tabWidth + (tabWidth - indicatorWidth) / 2;

  return (
    <View style={styles.tabIndicatorContainer}>
      <View
        style={[
          styles.tabIndicator,
          { left: leftPosition, width: indicatorWidth },
        ]}
      />
    </View>
  );
};

const CustomTabBar = (props: {
  state: { routes: { name: string }[]; index: number };
  layout: any;
  navigation: any;
}) => {
  return (
    <>
      <View className="pt-safe w-full dark:bg-primary-950">
        {/* BackButton */}
        <View className="w-full flex-row items-center justify-between px-5">
          <Pressable
            className="flex-row items-center gap-2"
            onPress={() => {
              router.back();
            }}
          >
            <Image
              source={require('../../../assets/images/back-icon.svg')}
              contentFit="contain"
              style={{ height: 12, width: 6 }}
            />
            <Text className="font-inter text-sm font-medium dark:text-black-50 ">
              Back
            </Text>
          </Pressable>

          <Text className="font-PoppinsBold text-xl font-bold dark:text-black-100">
            Bookings
          </Text>
          <Pressable className="flex-row items-center gap-4">
            <Image
              source={require('../../../assets/images/search-icon.png')}
              contentFit="contain"
              style={{ height: 18, width: 18 }}
            />
          </Pressable>
        </View>
      </View>
      <View style={styles.tabBar}>
        {props.state.routes.map((route, index) => {
          const isFocused = index === props.state.index;

          return (
            <Pressable
              style={styles.tabItem}
              key={index}
              onPress={() => {
                props.navigation.navigate(route.name);
              }}
            >
              <TabLabel focused={isFocused} title={route.name} />
            </Pressable>
          );
        })}
        <CustomTabBarIndicator
          layout={props.layout}
          navigation={props.navigation}
        />
      </View>
    </>
  );
};

export default function Bookings() {
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      initialLayout={initialLayout}
    >
      <Tab.Screen
        name="Upcoming Bookings"
        component={UpComingBooking}
        options={{ tabBarLabel: 'Upcoming Bookings' }}
      />
      <Tab.Screen
        name="Past Bookings"
        component={PastBooking}
        options={{ tabBarLabel: 'Past Bookings' }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#020717',
    height: 40,
    position: 'relative',
    flexDirection: 'row',
    marginTop: 15,
    // marginHorizontal:20
  },
  activeLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'bold',
    fontFamily: 'AirbnbW_Md',
    color: '#F2F2F2',
  },
  inactiveLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'normal',
    fontFamily: 'AirbnbW_Bk',
    color: '#F2F2F2',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIndicatorContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 2,
    backgroundColor: '#071952',
  },
  tabIndicator: {
    backgroundColor: '#CCCCCC',
    height: '100%',
    position: 'absolute',
    bottom: 0,
  },
});
