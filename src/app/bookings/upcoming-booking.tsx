import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, View } from 'react-native';

import BookingsCard from '@/components/bookings-card';

const handleBookingPress = () => {
  router.push('/booking-details');
};

export default function UpComingBookings() {
  return (
    <View className="w-full flex-1 px-5 dark:bg-primary-950">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="mt-6">
          <Image
            source={require('../../../assets/images/greatoffer.png')}
            contentFit="contain"
            className="w-full, h-[138px]"
          />
        </View>
        <View className="mt-3 flex-1">
          <FlashList
            data={Data}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={50}
            // keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <BookingsCard
                date={item.date}
                boarding={item.boarding}
                alighting={item.alighting}
                boardingTiming={item.boardingTiming}
                travelTiming={item.travelTiming}
                alightingTiming={item.alightingTiming}
                TravelerName={item.TravelerName}
                TravelerDetails={item.TravelerDetails}
                text={item.text}
                className={item.className}
                classNameText={item.classNameText}
                profileImage={item.profileImage}
                onPress={handleBookingPress}
              />
            )}
          />
          <View className="pb-6" />
        </View>
      </ScrollView>
    </View>
  );
}

const Data = [
  {
    date: '23 April,2024',
    boarding: 'FGR',
    alighting: 'IND',
    boardingTiming: '10:05 PM',
    travelTiming: '2:05 AM',
    alightingTiming: '20:2 PM',
    TravelerName: 'Kaushiki ojha',
    TravelerDetails: 'Female, 23yrs, Germany',
    text: 'Unpaid',
    className: 'dark:bg-darkblackbutton',
    classNameText: 'dark:text-black-0',
    profileImage: require('../../../assets/images/profile.png'),
  },
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
  {
    date: '23 April,2024',
    boarding: 'FGR',
    alighting: 'IND',
    boardingTiming: '10:05 PM',
    travelTiming: '2:05 AM',
    alightingTiming: '20:2 PM',
    TravelerName: 'Kaushiki ojha',
    TravelerDetails: 'Female, 23yrs, Germany',
    text: 'Unpaid',
    className: 'dark:bg-darkblackbutton',
    classNameText: 'dark:text-black-0',
    profileImage: require('../../../assets/images/profile.png'),
  },
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
  {
    date: '23 April,2024',
    boarding: 'FGR',
    alighting: 'IND',
    boardingTiming: '10:05 PM',
    travelTiming: '2:05 AM',
    alightingTiming: '20:2 PM',
    TravelerName: 'Kaushiki ojha',
    TravelerDetails: 'Female, 23yrs, Germany',
    text: 'Unpaid',
    className: 'dark:bg-darkblackbutton',
    classNameText: 'dark:text-black-0',
    profileImage: require('../../../assets/images/profile.png'),
  },
];
