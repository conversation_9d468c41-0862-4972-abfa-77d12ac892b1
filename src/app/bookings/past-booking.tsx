import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';

import BookingsCard from '@/components/bookings-card';

const handleBookingPress = () => {
  router.push('/booking-details');
};

export default function PastBookings() {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  // Initialize databases client (typically in a separate config/lib file)
  // const databases = new Databases(client);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        // const response = await databases.listDocuments(
        //   process.env.DATABASE_ID, // Use environment variables
        //   process.env.COLLECTION_ID_AIRPORTS // Use environment variables
        // );
        // setDocuments(response.documents);
        // console.log(typeof documents);
        // console.log(response);
      } catch (error) {
        console.error('Failed to fetch documents:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  return loading ? (
    <View className="items-center justify-center">
      <ActivityIndicator size={'large'} color="white" />
    </View>
  ) : documents.length === 0 ? (
    <View className="items-center justify-center">
      <Text>No bookings found</Text>
    </View>
  ) : (
    <View className="w-full flex-1 px-5 dark:bg-primary-950">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="mt-6">
          <Image
            source={require('../../../assets/images/greatoffer.png')}
            contentFit="contain"
            className="h-[138px] w-full"
          />
        </View>
        <View className="mt-3 flex-1">
          <FlashList
            data={Data} // Changed from Data to documents
            showsVerticalScrollIndicator={false}
            estimatedItemSize={50}
            renderItem={({ item }) => (
              <BookingsCard
                date={item.date}
                boarding={item.boarding}
                alighting={item.alighting}
                boardingTiming={item.boardingTiming}
                travelTiming={item.travelTiming}
                alightingTiming={item.alightingTiming}
                TravelerName={item.TravelerName}
                TravelerDetails={item.TravelerDetails}
                text={item.text}
                className={item.className}
                classNameText={item.classNameText}
                profileImage={item.profileImage}
                onPress={handleBookingPress}
              />
            )}
          />
          <View className="pb-6" />
        </View>
      </ScrollView>
    </View>
  );
}

const Data = [
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
  {
    date: '17 Jan,2024',
    boarding: 'LGA',
    alighting: 'RTM',
    boardingTiming: '02:05 PM',
    travelTiming: '1:30 hr AM',
    alightingTiming: '05:2 PM',
    TravelerName: 'Shreesh',
    TravelerDetails: 'Male, 20yrs, India',
    text: 'Paid',
    className: 'dark:bg-secondary-50',
    classNameText: 'dark:text-secondary-700',
    profileImage: require('../../../assets/images/avatar.png'),
  },
];
