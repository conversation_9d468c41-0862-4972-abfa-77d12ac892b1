// Import  global CSS file
import '../../global.css';

import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { ThemeProvider } from '@react-navigation/native';
import { Stack } from 'expo-router';
import { useSegments } from 'expo-router';
import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';

import { APIProvider } from '@/api';
import { loadSelectedTheme, useAuth } from '@/lib';
import { useThemeConfig } from '@/lib/use-theme-config';
import { useAirportStore } from '@/store/airport-store';

export { ErrorBoundary } from 'expo-router';

export const unstable_settings = {
  initialRouteName: 'index',
};

loadSelectedTheme();
// Prevent the splash screen from auto-hiding before asset loading is complete.
// SplashScreen.preventAutoHideAsync();
// Set the animation options. This is optional.
// SplashScreen.setOptions({
//   duration: 500,
//   fade: true,
// });

export default function RootLayout() {
  const segments = useSegments();

  console.log(`router is /${segments.join('/')}`);

  return (
    <Providers>
      <Stack>
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="onboarding" options={{ headerShown: false }} />
        <Stack.Screen name="(app)" options={{ headerShown: false }} />
        <Stack.Screen name="auth" options={{ headerShown: false }} />
        <Stack.Screen name="welcome" options={{ headerShown: false }} />
        <Stack.Screen
          name="form/companion-required"
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ready-as-companion"
          options={{ headerShown: false }}
        />
        <Stack.Screen name="wallet" options={{ headerShown: false }} />
        <Stack.Screen name="feed/[id]" options={{ headerShown: false }} />
        <Stack.Screen name="payment" options={{ headerShown: false }} />
        <Stack.Screen name="bookings" options={{ headerShown: false }} />
        <Stack.Screen name="booking-details" options={{ headerShown: false }} />
        <Stack.Screen name="personal-info" options={{ headerShown: false }} />
        <Stack.Screen name="notifications" options={{ headerShown: false }} />
        <Stack.Screen name="feedback" options={{ headerShown: false }} />
        <Stack.Screen name="refer-to" options={{ headerShown: false }} />
        <Stack.Screen name="chat-screens" options={{ headerShown: false }} />
        <Stack.Screen name="match-screens" options={{ headerShown: false }} />
        {/*<Stack.Screen name="password" options={{ headerShown: false }} />*/}
        <Stack.Screen name="policy" options={{ headerShown: false }} />
        <Stack.Screen name="transactions" options={{ headerShown: false }} />
        <Stack.Screen name="settings" options={{ headerShown: false }} />
        <Stack.Screen name="switch-account" options={{ headerShown: false }} />
        <Stack.Screen name="leader-board" options={{ headerShown: false }} />
      </Stack>
    </Providers>
  );
}

function Providers({ children }: { children: React.ReactNode }) {
  const theme = useThemeConfig();
  const auth = useAuth();
  const { fetchAirports } = useAirportStore();

  // Fetch airports when the app loads
  useEffect(() => {
    if (auth.status === 'signIn') {
      fetchAirports();
    }
  }, [fetchAirports, auth.status]);

  return (
    <GestureHandlerRootView
      style={styles.container}
      className={theme.dark ? `dark` : undefined}
    >
      <KeyboardProvider>
        <ThemeProvider value={theme}>
          <APIProvider>
            <BottomSheetModalProvider>
              {children}
              <FlashMessage position="top" />
            </BottomSheetModalProvider>
          </APIProvider>
        </ThemeProvider>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
