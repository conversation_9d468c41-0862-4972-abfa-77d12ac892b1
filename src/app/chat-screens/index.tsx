import Entypo from '@expo/vector-icons/Entypo';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { router, useNavigation } from 'expo-router';
import React, { useRef, useState } from 'react';
import { KeyboardAvoidingView } from 'react-native';
import {
  Image,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  View,
} from 'react-native';

import { LeftMsg, RightMsg } from '../../components/chat-screen-msgs';

export default function ChatScreen() {
  const navigation = useNavigation();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const [isVisible, setIsVisible] = useState(false);

  const handlePresentModal = () => {
    bottomSheetModalRef.current?.present();
    setIsVisible(true);
  };

  const handleDismissModal = () => {
    bottomSheetModalRef.current?.dismiss();
    setIsVisible(false);
  };

  return (
    <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
      {isVisible ? (
        <View className="absolute z-10 size-full flex-1 bg-[black] opacity-70" />
      ) : null}
      <View className="pt-safe flex-1">
        {/* Header */}
        <View className="flex-row items-center justify-between bg-[#20202080] px-5 py-3">
          {/* Profile details and back button */}
          <View className="flex-row items-center">
            <Pressable onPress={() => navigation.goBack()}>
              <Entypo
                name="chevron-thin-left"
                size={18}
                color="#CCCCCC"
                className="pr-3"
              />
            </Pressable>
            <Pressable
              className="flex-row items-center"
              onPress={() => router.push('/chat-screens/profile')}
            >
              <Image
                source={require('../../../assets/images/profile.png')}
                className="ml-3 size-10"
              />
              <View className="px-3">
                <Text className="font-PoppinsMedium text-base font-medium text-charcoal-50">
                  Jason Allen
                </Text>
                <View className="flex-row items-center">
                  <Text className="font-PoppinsRegular text-xs font-normal text-black-300">
                    LAX
                  </Text>
                  <Image
                    source={require('../../../assets/images/flight1.png')}
                    className="mx-0.5 size-3"
                  />
                  <Text className="font-PoppinsRegular text-xs font-normal text-black-300">
                    JFK
                  </Text>
                  <Image
                    source={require('../../../assets/images/flight1.png')}
                    className="mx-0.5 size-3"
                  />
                  <Text className="font-PoppinsRegular text-xs font-normal text-black-300">
                    LHR
                  </Text>
                </View>
              </View>
            </Pressable>
          </View>

          <View className="flex-row items-center">
            {/* type */}
            <Pressable
              className="mx-3 rounded-lg bg-primary-50 px-6 py-3"
              onPress={() => router.push('/payment/')}
            >
              {/* here mb-5 for putting it on top */}
              <Text className="font-inter text-base font-medium text-blue">
                Pay
              </Text>
            </Pressable>

            {/* header 3 dot menu */}
            <Pressable className="rounded-lg " onPress={handlePresentModal}>
              <Image
                source={require('../../../assets/images/dotwhite.png')}
                className=" size-6"
              />
            </Pressable>
          </View>
        </View>

        {/* Chat screen */}
        <ScrollView className="px-5" showsVerticalScrollIndicator={false}>
          {/* Day or Time of chat */}
          <View className="w-full flex-row items-center justify-center py-5">
            <View className="self-start rounded-lg bg-primary-50 px-2.5 py-1">
              <Text className="font-inter text-sm font-semibold text-blue">
                Yesterday
              </Text>
            </View>
          </View>

          {/* message left*/}
          <LeftMsg
            message={'Hi, this is Jason allen your match companion, hope ....'}
            isTrue={true}
            time={'12:00am'}
            unread={false}
          />

          {/* message left*/}
          <LeftMsg
            message={'How are you?'}
            isTrue={false}
            time={'12:00am'}
            unread={false}
          />

          {/* message right*/}
          <RightMsg
            message={'Hello , I am good'}
            isTrue={false}
            time={'12:20am'}
            unread={false}
          />

          {/* Day or Time of chat */}
          <View className="w-full flex-row items-center justify-center py-5">
            <View className="self-start rounded-lg bg-primary-50 px-2.5 py-1">
              <Text className="font-inter text-sm font-semibold text-blue">
                Today
              </Text>
            </View>
          </View>

          {/* message left*/}
          <LeftMsg
            message={'At with time we can meet at airport?'}
            isTrue={false}
            time={'2:49pm'}
            unread={false}
          />

          {/* message right*/}
          <RightMsg
            message={'I think 2:30pm will be okay. on this friday'}
            isTrue={false}
            time={'3:00pm'}
            unread={false}
          />

          {/* message left*/}
          <LeftMsg
            message={
              'okay, i’ll be there around 2:15pm outside the airport, you can meet me at gate number 2 before onboarding.'
            }
            isTrue={false}
            time={'3:05pm'}
            unread={false}
          />

          {/* message left*/}
          <LeftMsg
            message={'It is ok for you ?'}
            isTrue={false}
            time={'3:05pm'}
            unread={false}
          />

          {/* message right*/}
          <RightMsg
            message={'Yaa, sure we can do this , i don’t have issue for that'}
            isTrue={false}
            time={'3:05pm'}
            unread={true}
          />
        </ScrollView>

        {/* keyboard */}
        <View className="mb-5 border-t border-primary-50">
          <View className="my-4 w-full flex-row items-center justify-evenly px-5">
            <View
              className={`flex-row items-center justify-between rounded-2xl border border-black-0 bg-primary-950 px-4`}
            >
              <TextInput
                className={`h-12 w-4/5 font-PoppinsRegular text-sm font-normal text-black-50`}
                placeholder={'Type your message'}
                placeholderTextColor={'#B3B3B3'}
                // multiline={true}
                // numberOfLines={4}
              />
              <Image
                source={require('../../../assets/images/Send.png')}
                className="aspect-square w-[6%]"
                resizeMode="contain"
                tintColor={'#E6E6E6'}
              />
            </View>
            <Pressable>
              <Image
                source={require('../../../assets/images/recorder.png')}
                className="ml-2 size-6"
                resizeMode="contain"
              />
            </Pressable>
          </View>
        </View>

        {/* Bottom Sheet Modal */}
        <BottomSheetModal
          ref={bottomSheetModalRef}
          index={0}
          snapPoints={['50%']}
          onDismiss={handleDismissModal}
          backgroundStyle={{ backgroundColor: '#020717' }}
          handleIndicatorStyle={{ backgroundColor: 'transparent' }}
          enablePanDownToClose={true}
        >
          <View className="flex-1 px-5">
            <Pressable onPress={handleDismissModal} className="py-5">
              <Image
                source={require('../../../assets/images/cancel.png')}
                resizeMode="contain"
                className="size-7"
              />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/profile1.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  View Profile
                </Text>
              </View>
              <Entypo name="chevron-thin-right" size={18} color="#FDFDFD" />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/helpcenter.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Help Center
                </Text>
              </View>
              <Entypo name="chevron-thin-right" size={18} color="#FDFDFD" />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/report.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Report Companion
                </Text>
              </View>
              <Entypo name="chevron-thin-right" size={18} color="#FDFDFD" />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/Bin.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Delete Chat
                </Text>
              </View>
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/idk.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <View className="flex-row flex-wrap items-center">
                  <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                    Close Chat
                  </Text>
                  <Text className="font-PoppinsMedium text-xs font-medium text-black-100">
                    (this option only available when user have paid the monetary
                    value to companion)
                  </Text>
                </View>
              </View>
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/block.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Block
                </Text>
              </View>
            </Pressable>
          </View>
        </BottomSheetModal>
      </View>
    </KeyboardAvoidingView>
  );
}
