import { Stack } from 'expo-router';
const ReadyAsCompanionLayout = () => {
  return (
    <Stack initialRouteName="companion-details">
      <Stack.Screen name="companion-details" options={{ headerShown: false }} />
      <Stack.Screen name="flight-details" options={{ headerShown: false }} />
      <Stack.Screen name="upload-photo" options={{ headerShown: false }} />
    </Stack>
  );
};
export default ReadyAsCompanionLayout;
