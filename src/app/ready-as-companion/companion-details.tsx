import { Env } from '@env';
import { zodResolver } from '@hookform/resolvers/zod';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button, Switch } from '@/components/ui';
import { companionSchema } from '@/form-schema/form-schema';
import { account, databases } from '@/lib/appwrite';
import { cn } from '@/lib/utils';
import { useLanguageStore } from '@/store/language-store';
import { userTypeStore } from '@/store/user-type-store';

const CompanionDetails = () => {
  const [isLoading, setIsLoading] = useState(false);
  console.log(
    'Env.COLLECTION_ID, Env.COLLECTION_BOOKING_COMPANION,',
    Env.COLLECTION_ID,
    Env.COLLECTION_BOOKING_COMPANION
  );
  const { user } = userTypeStore((state) => {
    return {
      changeUser: state.changeUser,
      user: state.user,
    };
  });

  const {
    watch,
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<z.infer<typeof companionSchema>>({
    resolver: zodResolver(companionSchema),
    defaultValues: {
      name: '',
      about: '',
      typeOfTraveller: 'SOLO',
      genderPreference: 'MALE',
      openToAllGender: false,
      languages: [],
      gender: 'MALE',
    },
  });

  useEffect(() => {
    async function userDetails() {
      try {
        if (!user) {
          return;
        }

        //set the basic account details
        reset({
          name: user.name,
        });

        // try to get the user additional details
        try {
          const details = await databases.getDocument(
            Env.COLLECTION_ID,
            Env.COLLECTION_BOOKING_COMPANION,
            `${user?.$id}`
          );
          console.log('details exits already', details);

          // If languages exist in the database, then set it in the language store
          if (details.languages && Array.isArray(details.languages)) {
            details.languages.forEach((lang) => {
              addLanguage(lang.$id, lang.name);
            });
          }

          reset({
            name: user.name,
            about: details.about,
            typeOfTraveller: details.typeOfTraveller,
            genderPreference: details.genderPreference,
            openToAllGender: details.openToAllGender,
            gender: details.gender,
          });
        } catch (error: any) {
          if (error.code === 404) {
            console.log('Companion do not exist yet - will create on save');
            // setCompanionId(user.$id);
          } else {
            // Handle other potential errors
            console.error('Error fetching user details:', error);
          }
        }
      } catch (err) {
        console.error('Error getting user account:', err);
      }
    }
    userDetails();
  }, []);

  const onSubmit = handleSubmit(
    async (data: z.infer<typeof companionSchema>) => {
      console.log('data is', data);
      setIsLoading(true);
      try {
        //update basic account details
        await account.updateName(data.name);

        //prepare the document data
        const documentData = {
          about: data.about,
          typeOfTraveller: data.typeOfTraveller,
          gender: data.gender,
          openToAllGender: data.openToAllGender,
          genderPreference: data.genderPreference,
          languages: languageThatTravellerSpeaks.map((i) => i.id) || [],
        };

        {
          //create a new document if user details does not exist
          try {
            const companion = await databases.createDocument(
              Env.COLLECTION_ID,
              Env.COLLECTION_BOOKING_COMPANION,
              `${user?.$id}`,
              documentData
            );
            console.log('companion created successfully', companion);
          } catch (createError: any) {
            console.error('Create document error details:', createError);
            throw createError;
          }
        }
      } catch (error) {
        console.error('Error saving user details:', error);
      } finally {
        setIsLoading(false);
      }
      // setSelectedStep(selectedStep + 1);
      router.push({
        pathname: '/ready-as-companion/flight-details',
      });
    },
    (errors) => {
      console.log('errors in form submission is', errors);
    }
  );

  const { languageThatTravellerSpeaks, addLanguage } = useLanguageStore(
    (state) => {
      return {
        languageThatTravellerSpeaks: state.languageThatTravellerSpeaks,
        addLanguage: state.addLanguage,
      };
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={1}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />
        <View className="flex-1 gap-8">
          <View className="mt-8 gap-2">
            <Text className=" font-PoppinsBold text-[20px] font-bold leading-[30px] dark:text-black-50">
              Add Companion Details
            </Text>
            <Text className="dark: font-inter text-sm font-medium leading-5 text-black-300">
              Add Companion details to connect with traveler
            </Text>
          </View>

          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            <View className="flex-1 gap-[18px] ">
              <InputLabelled label="Companion’s First Name *">
                <Controller
                  control={control}
                  name="name"
                  render={({ field: { value, onChange } }) => (
                    <InputText
                      value={value}
                      onChangeText={onChange}
                      placeholder="Enter your name"
                      placeholderTextColor={''}
                      iconSourceFirst={require('../../../assets/images/user-text-input.svg')}
                      iconStyleFirst={{
                        height: 20,
                        width: 20,
                        tintColor: 'white',
                      }}
                    />
                  )}
                />
              </InputLabelled>
              {errors.name && (
                <Text className="text-red-500">
                  {errors.name.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Are you a ______ Traveler?">
                <Controller
                  name="typeOfTraveller"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <View className="mt-2.5  flex-row justify-between  rounded-[12px] border border-borderdark bg-black-0 px-5 py-[14px] text-black-0 dark:bg-bgtextInput">
                      <Pressable onPress={() => onChange('SOLO')}>
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              ' bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
                                value === 'SOLO',
                            }
                          )}
                        >
                          <Image
                            source={require('../../../assets/images/plane.png')}
                            contentFit="contain"
                            style={{ height: 12, width: 12 }}
                          />
                          <Text
                            className={cn(
                              'text-black-0 dark:text-black-300',
                              value === 'SOLO'
                                ? 'dark:text-black-200 '
                                : 'text-black-300'
                            )}
                          >
                            Solo
                          </Text>
                        </View>
                      </Pressable>

                      <Pressable onPress={() => onChange('FAMILY')}>
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              ' bg-black-0 dark:bg-primary-900 rounded-[12px]':
                                value === 'FAMILY',
                            }
                          )}
                        >
                          <Image
                            source={require('../../../assets/images/plane.png')}
                            contentFit="contain"
                            style={{ height: 12, width: 12 }}
                          />
                          <Text
                            className={cn(
                              'text-black-0 dark:text-black-300',
                              value === 'FAMILY'
                                ? 'dark:text-black-200'
                                : 'text-black-300'
                            )}
                          >
                            Family
                          </Text>
                        </View>
                      </Pressable>

                      <Pressable onPress={() => onChange('GROUP')}>
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              'bg-blue dark:bg-primary-900 rounded-[12px] text-black-0':
                                value === 'GROUP',
                            }
                          )}
                        >
                          <Image
                            source={require('../../../assets/images/plane.png')}
                            contentFit="contain"
                            style={{ height: 12, width: 12 }}
                          />
                          <Text
                            className={cn(
                              'dark : text-black-300',
                              value === 'GROUP'
                                ? 'dark:text-black-200'
                                : 'text-black-300'
                            )}
                          >
                            Group
                          </Text>
                        </View>
                      </Pressable>
                    </View>
                  )}
                />
              </InputLabelled>
              {errors.typeOfTraveller && (
                <Text className="text-red-500">
                  {errors.typeOfTraveller.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Your Gender?">
                <Controller
                  control={control}
                  name="gender"
                  render={({ field: { value, onChange } }) => (
                    <View className="mt-2.5 flex-row justify-between rounded-[12px] border border-borderdark bg-black-0 px-5 py-[14px] text-black-0 dark:bg-bgtextInput">
                      <Pressable onPress={() => onChange('MALE')}>
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              ' bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
                                value === 'MALE',
                            }
                          )}
                        >
                          <Text
                            className={cn(
                              'text-black-0 dark:text-black-300',
                              value === 'MALE'
                                ? 'dark:text-black-200 '
                                : 'text-black-300'
                            )}
                          >
                            Male
                          </Text>
                        </View>
                      </Pressable>

                      <Pressable onPress={() => onChange('FEMALE')}>
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              ' bg-black-0 dark:bg-primary-900 rounded-[12px]':
                                value === 'FEMALE',
                            }
                          )}
                        >
                          <Text
                            className={cn(
                              'text-black-0 dark:text-black-300',
                              value === 'FEMALE'
                                ? 'dark:text-black-200'
                                : 'text-black-300'
                            )}
                          >
                            Female
                          </Text>
                        </View>
                      </Pressable>
                      <Pressable onPress={() => onChange('OTHERS')}>
                        <View
                          className={cn(
                            'flex-row gap-2 items-center px-2 py-2 ',
                            {
                              'bg-blue dark:bg-primary-900 rounded-[12px] text-black-0':
                                value === 'OTHERS',
                            }
                          )}
                        >
                          <Text
                            className={cn(
                              'dark : text-black-300',
                              value === 'OTHERS'
                                ? 'dark:text-black-200'
                                : 'text-black-300'
                            )}
                          >
                            Others
                          </Text>
                        </View>
                      </Pressable>
                    </View>
                  )}
                />
              </InputLabelled>
              {errors.gender && (
                <Text className="text-red-500">
                  {errors.gender.message?.toString()}
                </Text>
              )}

              <View>
                <View className="flex-row items-center justify-between">
                  <InputLabelled
                    label="Gender Preference"
                    className="flex-row items-center gap-4"
                  >
                    <Controller
                      control={control}
                      name="openToAllGender"
                      render={({ field: { onChange, value } }) => (
                        <Switch
                          accessibilityLabel=""
                          label="Open for all genders"
                          className="text-black-300"
                          checked={value}
                          onChange={(checked) => {
                            onChange(checked);
                          }}
                        />
                      )}
                    />
                  </InputLabelled>
                </View>
                {!watch('openToAllGender') && (
                  <Controller
                    control={control}
                    name="genderPreference"
                    render={({ field: { onChange, value } }) => (
                      <InputText
                        placeholder="Enter gender preference"
                        value={value}
                        onChangeText={(text) => onChange(text.toUpperCase())}
                      />
                    )}
                  />
                )}
                {errors.genderPreference && (
                  <Text className="text-red-500">
                    {errors.genderPreference.message?.toString()}
                  </Text>
                )}
              </View>

              <InputLabelled label="Language Companion Speaks *">
                <Controller
                  control={control}
                  name="languages"
                  render={({ field: { value, onChange } }) => (
                    <Pressable
                      onPress={() => {
                        router.push('/form/companion-required/search');
                      }}
                    >
                      <View className="mt-2.5 flex-row flex-wrap items-center gap-2 rounded-[12px] border border-borderdark p-5 dark:bg-bgtextInput">
                        {languageThatTravellerSpeaks.length === 0 ? (
                          <Text className="text-[14px] text-black-300">
                            Select Language
                          </Text>
                        ) : (
                          languageThatTravellerSpeaks.map((language) => (
                            <Text
                              key={language.id}
                              className="font-inter text-[14px] text-black-0 dark:text-black-100"
                            >
                              {language.name}
                            </Text>
                          ))
                        )}
                      </View>
                    </Pressable>
                  )}
                />
              </InputLabelled>
              {errors.languages && (
                <Text className="text-red-500">
                  {errors.languages.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Write About You">
                <Controller
                  control={control}
                  name="about"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder="Write about yourself"
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
              </InputLabelled>
              {errors.about && (
                <Text className="text-red-500">
                  {errors.about.message?.toString()}
                </Text>
              )}
            </View>
          </ScrollView>
        </View>

        <Button
          variant="secondary"
          label="Next"
          className="mb-2 mt-8"
          loading={isLoading}
          onPress={onSubmit}
        />
      </View>
    </SafeAreaView>
  );
};

export default CompanionDetails;
