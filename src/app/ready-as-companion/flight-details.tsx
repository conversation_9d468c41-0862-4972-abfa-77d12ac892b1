import { zodResolver } from '@hookform/resolvers/zod';
import DateTimePicker, {
  type DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Platform,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputErrorMsg from '@/components/input-error-msg';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { flightSchema } from '@/form-schema/form-schema';

type Flight = {
  id: number;
  name: string;
};

const CompanionFlightDetails = () => {
  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [swapped, setSwapped] = useState(false);
  const toggleSwapp = () => {
    setSwapped(!swapped);
  };

  // State to store the added interconnected flights
  const [flights, setFlights] = useState<Flight[]>([]);

  // Function to add a new flight to the list
  const toggleAddInterCF = () => {
    setFlights([
      ...flights,
      {
        id: flights.length,
        name: 'InterConnected Flight',
      },
    ]);
  };

  const toggleDatepicker = () => {
    setShowDatePicker(true);
  };

  const onDateChange = (
    event: DateTimePickerEvent,
    selectedDate: Date | undefined
  ) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'android' ? false : true);
    setDate(currentDate);

    const formattedDate = currentDate.toDateString();
    setValue('flightDate', formattedDate, { shouldValidate: true });
  };

  const toggleTimepicker = () => {
    setShowTimePicker(true);
  };

  const onTimeChange = (
    event: DateTimePickerEvent,
    selectedTime: Date | undefined
  ) => {
    const currentTime = selectedTime || time;
    setShowTimePicker(Platform.OS === 'android' ? false : true);
    setTime(currentTime);

    const formattedTime = currentTime.toTimeString().split(' ')[0];
    setValue('flightTime', formattedTime, { shouldValidate: true });
  };

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof flightSchema>>({
    resolver: zodResolver(flightSchema),
    defaultValues: {
      flightCompany: '',
      flightPNR: '',
      flightDate: '',
      flightTime: '',
    },
  });

  const onSubmit = handleSubmit(
    (data: z.infer<typeof flightSchema>) => {
      console.log('submit with data', data);
      console.log('form errors are', errors);
      // setSelectedStep(selectedStep + 1);
      router.push({
        pathname: '/form/companion-required/photo-uploads',
        // params: { selectedStep: selectedStep + 1 },
      });
    },
    (errors) => {
      console.log('Form validation failed:', errors);
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={2}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />
        <View className=" flex-1 gap-8">
          <View className="mt-4 gap-2 ">
            <Text className="font-PoppinsBold text-[20px] font-bold leading-[30px] text-black-950 dark:text-black-0 ">
              Add Flight Details
            </Text>
            <Text className="font-inter text-[14px] font-medium leading-5 text-black-950 dark:text-black-300">
              Add companion flight details to connect with traveler
            </Text>
          </View>

          <ScrollView
            className="mb-5 w-full flex-1"
            showsVerticalScrollIndicator={false}
          >
            <View className=" w-full flex-1 gap-5">
              <View className="">
                <View className="flex-row items-center justify-between">
                  <Text className="font-PoppinsMedium text-[16px] font-medium leading-[24px] text-black-950 dark:text-black-50">
                    From & To Destination
                  </Text>
                  <TouchableOpacity onPress={toggleAddInterCF}>
                    <View className="border-coustomborder rounded-[8px] border bg-black-0 p-[6px] dark:bg-primary-950">
                      <Image
                        source={require('../../../assets/images/plus.png')}
                        contentFit="contain"
                        style={{ height: 20, width: 20 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
                <View className="flex-row items-center gap-2">
                  <Pressable
                    className="flex-1"
                    onPress={() => {
                      router.push(
                        '/form/companion-required/destination-search'
                      );
                    }}
                  >
                    <View className="dark: mt-2 flex-row items-center gap-2 rounded-t-[12px] border-x border-t border-customborder bg-bgtextInput px-5 py-[14px]">
                      <Image
                        source={require('../../../assets/images/location.png')}
                        contentFit="contain"
                        style={{ width: 12, height: 15 }}
                      />
                      <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-300">
                        {swapped ? 'From where?' : 'Where to?'}
                      </Text>
                    </View>

                    {/* Add Interconnected Flights */}
                    <View>
                      {/* Map over flights and display them */}
                      {flights.map((flight, index) => (
                        <View
                          key={flight.id} // Use flight.id as the key
                          className="flex-row items-center justify-between border bg-bgtextInput px-5 py-[14px] dark:border-customborder"
                        >
                          <View className="flex-row items-center gap-2">
                            <Image
                              source={require('../../../assets/images/location.png')}
                              contentFit="contain"
                              style={{ width: 12, height: 15 }}
                            />
                            <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-300">
                              {flight.name} {index + 1}{' '}
                              {/* Display flight number */}
                            </Text>
                          </View>
                          <Pressable
                            // className="bg-red-900"
                            onPress={() => {
                              // Remove this specific flight by its index
                              setFlights(flights.filter((_, i) => i !== index));
                            }}
                          >
                            <Image
                              source={require('../../../assets/images/circlecross.png')}
                              contentFit="contain"
                              style={{ width: 20, height: 20 }}
                            />
                          </Pressable>
                        </View>
                      ))}
                    </View>

                    <View className="border-coustomborder flex-row items-center gap-2 rounded-b-[12px] border px-5 py-[14px] text-[14px] font-normal leading-5 dark:bg-bgtextInput ">
                      <Image
                        source={require('../../../assets/images/location.png')}
                        contentFit="contain"
                        style={{ width: 12, height: 15 }}
                      />

                      <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-300">
                        {swapped ? 'where to?' : 'From Where?'}
                      </Text>
                    </View>
                  </Pressable>
                  <TouchableOpacity onPress={toggleSwapp}>
                    <View className="border-coustomborder rounded-[8px] border bg-black-0 p-[6px] dark:bg-primary-950">
                      <Image
                        source={require('../../../assets/images/swaparrow.png')}
                        contentFit="contain"
                        style={{ height: 20, width: 20 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>

              <InputLabelled label="Flight Company">
                <Controller
                  control={control}
                  name="flightCompany"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder="Airline"
                      value={value}
                      onChangeText={onChange}
                      iconSourceFirst={require('../../../assets/images/plane.png')}
                      iconStyleFirst={{ height: 14.998, width: 15 }}
                    />
                  )}
                />
              </InputLabelled>
              {errors && errors.flightCompany && (
                <Text className="text-red-500">
                  {errors.flightCompany.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Flight PNR/Ref/Confirmation Number">
                <Controller
                  name="flightPNR"
                  control={control}
                  render={({ field, fieldState }) => {
                    return (
                      <>
                        <InputText
                          placeholder="89CBSK6"
                          value={field.value}
                          onChangeText={(value) => {
                            field.onChange(value);
                          }}
                          iconSourceFirst={require('../../../assets/images/user-text-input.svg')}
                          iconStyleFirst={{ height: 15.981, width: 13.333 }}
                        />

                        {fieldState.error?.message && (
                          <InputErrorMsg
                            message={fieldState.error.message}
                          ></InputErrorMsg>
                        )}
                      </>
                    );
                  }}
                />
              </InputLabelled>

              <InputLabelled label="Flight Date">
                {Platform.OS === 'ios' ? (
                  <Controller
                    name="flightDate"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <Pressable
                          onPress={() => setShowDatePicker(true)}
                          className="mt-2 items-start rounded-xl border border-customborder dark:bg-bgtextInput "
                        >
                          <DateTimePicker
                            value={date}
                            mode="date"
                            display="default"
                            onChange={onDateChange}
                          />
                        </Pressable>
                        {fieldState?.error?.message && (
                          <InputErrorMsg message={fieldState?.error?.message} />
                        )}
                      </>
                    )}
                  />
                ) : Platform.OS === 'android' ? (
                  <Controller
                    name="flightDate"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <InputText
                          editable={false}
                          onPress={toggleDatepicker}
                          placeholder="Select Date"
                          value={field.value}
                          onChangeText={(num) => {
                            field.onChange(num);
                          }}
                          iconSourceFirst={require('../../../assets/images/calender.png')}
                          iconStyleFirst={{ height: 15, width: 15 }}
                        />
                        {fieldState.error?.message && (
                          <InputErrorMsg
                            message={fieldState.error.message}
                          ></InputErrorMsg>
                        )}
                        {showDatePicker && (
                          <DateTimePicker
                            value={date}
                            mode="date"
                            display="default"
                            onChange={onDateChange}
                          />
                        )}
                      </>
                    )}
                  />
                ) : null}
              </InputLabelled>

              <InputLabelled label="Flight Time">
                {Platform.OS === 'ios' ? (
                  <Controller
                    name="flightTime"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <Pressable
                          onPress={() => setShowTimePicker(true)}
                          className="mt-2 items-start rounded-xl border border-customborder dark:bg-bgtextInput "
                        >
                          <DateTimePicker
                            value={time}
                            mode="time"
                            display="default"
                            onChange={onTimeChange}
                          />
                        </Pressable>
                        {fieldState?.error?.message && (
                          <InputErrorMsg message={fieldState?.error?.message} />
                        )}
                      </>
                    )}
                  />
                ) : Platform.OS === 'android' ? (
                  <Controller
                    name="flightTime"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <InputText
                          editable={false}
                          onPress={toggleTimepicker}
                          value={field.value}
                          onChangeText={(value) => {
                            field.onChange(value);
                          }}
                          placeholder="Select Time"
                          iconSourceFirst={require('../../../assets/images/watch.png')}
                          iconStyleFirst={{ height: 15, width: 15 }}
                        />
                        {fieldState.error?.message && (
                          <InputErrorMsg
                            message={fieldState.error.message}
                          ></InputErrorMsg>
                        )}
                        {showTimePicker && (
                          <DateTimePicker
                            value={time}
                            mode="time"
                            display="default"
                            onChange={onTimeChange}
                          />
                        )}
                      </>
                    )}
                  />
                ) : null}
              </InputLabelled>
            </View>
          </ScrollView>
        </View>

        <View className="mb-5 flex-row items-center justify-between px-5">
          <Pressable
            onPress={() => {
              router.back();
            }}
          >
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-950">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-black-50">
                Back
              </Text>
            </View>
          </Pressable>
          <Pressable onPress={onSubmit}>
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-50">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-blue">
                Next
              </Text>
            </View>
          </Pressable>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CompanionFlightDetails;
