import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import React from 'react';

import HomeScreen from './home';
import PendingRequestScreen from './pending-request';

const TopTab = createMaterialTopTabNavigator();

const TopTabsLayout = () => {
  return (
    <TopTab.Navigator
      screenOptions={{
        tabBarStyle: {
          backgroundColor: 'white',
          borderBottomColor: 'black',
          borderBottomWidth: 1,
        },
        tabBarIndicatorStyle: {
          backgroundColor: 'black',
        },
      }}
    >
      <TopTab.Screen name="home" component={HomeScreen} />
      <TopTab.Screen name="pending-request" component={PendingRequestScreen} />
    </TopTab.Navigator>
  );
};

export default TopTabsLayout;
