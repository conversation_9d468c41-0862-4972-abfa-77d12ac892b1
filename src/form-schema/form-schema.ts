import { z } from 'zod';

const phoneRegex = new RegExp(
  /^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/
);
const travellerType = ['SOLO', 'FAMILY', 'GROUP'] as const;
const gender = ['MALE', 'FEMALE', 'OTHERS'] as const;
const status = [
  'DRAFT',
  'ACTIVE',
  'ASSIGNED',
  'CANCELLED',
  'COMPLETED',
] as const;
const typeOfUser = ['COMPANION', 'TRAVELLER'] as const;
export const userSchema = z.object({
  name: z
    .string({ required_error: 'Name is required' })
    .min(1, { message: 'Name is required' })
    .min(3, { message: 'Name must be at least 3 char long' }),
  // email: z
  //   .string({ required_error: 'Email is required' })
  //   .email({ message: 'Invalid email address' })
  //   .optional()
  //   .nullable(),
  // phoneNo: z
  //   .string({ required_error: 'Phone number is required' })
  //   .min(1, { message: 'Phone number is required' })
  //   .regex(phoneRegex, 'Invalid Phone number!'),
  bookingFor: z.string().optional(),
  typeOfTraveller: z.enum(travellerType),
  gender: z.enum(gender),
  genderPreference: z
    .enum(gender, {
      message: 'Gender Preference can be MALE | FEMALE | OTHERS',
    })
    .optional(),
  openToAllGenders: z.boolean().optional(),
  languages: z
    .array(z.string(), { message: 'At least one language is required' })
    .optional(),
  about: z
    .string({ required_error: 'About yourself is required' })
    .min(1, { message: 'About yourself is required' })
    .min(5, { message: 'Write about yourself should contain atleast 5 chars' }),
  typeOfUser: z.enum(typeOfUser).optional(),
  userProfile: z.string().optional(),
  status: z.enum(status).optional(),
});

export const flightSchema = z.object({
  flightPNR: z
    .string()
    .min(1, { message: 'Flight PNR number is required.' })
    .min(6, { message: 'Enter correct Flight PNR number.' }),
  flightCompany: z
    .string()
    .min(1, { message: 'Flight company name is required.' })
    .min(3, { message: 'Enter correct Flight Company name.' }),
  flightDate: z.string().min(1, { message: 'Flight Date is required.' }),
  flightTime: z.string().min(1, { message: 'Flight Time is required.' }),
});

export const bookingSchema = z.object({
  travellersPhoto: z
    .string()
    .min(1, { message: 'Traveler photo is required.' }),
  passportPhoto: z.string().min(1, { message: 'Passport photo is required.' }),
  companionCompensation: z
    .string({
      invalid_type_error: 'Enter the correct value for compensation value.',
    })
    .min(1, { message: 'Compensation value is required.' }),
});

export const companionSchema = z.object({
  name: z
    .string({ required_error: 'Name is required' })
    .min(1, { message: 'Name is required' })
    .min(3, { message: 'Name must be at least 3 char long' }),
  typeOfTraveller: z.enum(travellerType),
  gender: z.enum(gender),
  genderPreference: z.enum(gender, {
    message: 'Gender Preference can be MALE | FEMALE | OTHERS',
  }),
  openToAllGender: z.boolean(),
  languages: z
    .array(z.string(), { message: 'At least one language is required' })
    .optional(),
  about: z
    .string({ required_error: 'About yourself is required' })
    .min(1, { message: 'About yourself is required' })
    .min(5, { message: 'Write about yourself should contain atleast 5 chars' }),
});
