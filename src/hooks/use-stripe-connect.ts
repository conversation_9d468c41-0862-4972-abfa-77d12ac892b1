import { Env } from '@env';
import { useMutation } from '@tanstack/react-query';
import type <PERSON><PERSON> from 'stripe';

import { account } from '@/lib/appwrite';
import { getUserData, updateUserData } from '@/lib/user-data-service';

export type IStripeConnectAccountResponse = {
  success: boolean;
  accountId: string;
  capabilties: Stripe.Account.Capabilities;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
  charges_enabled: boolean;
  payouts_enabled: boolean;
  details_submitted: boolean;
  email?: string;
  business_profile?: {
    name?: string;
    url?: string;
  };
};

export const useStripeConnect = () => {
  // Mutation to create Connect account and get onboarding URL
  const createConnectAccount = useMutation({
    mutationFn: async () => {
      const user = await account.get();

      // Check if we already have a Stripe Connect Account ID in user data
      const userData = await getUserData();
      if (userData?.stripeConnectAccountId) {
        // Return the existing account ID if it exists
        return {
          success: true,
          accountId: userData.stripeConnectAccountId,
          capabilties: {} as Stripe.Account.Capabilities, // We don't have capabilities here, but the type expects it
        };
      }

      // If no account ID exists, create a new one
      const response = await fetch(
        `${Env.STRIPE_API_URL}/create-stripe-connect-account`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-appwrite-user-id': user.$id,
          },
          body: JSON.stringify({
            email: user.email,
            name: user.name,
            phone: user.phone,
          }),
        }
      );

      if (!response.ok) throw new Error('Failed to create connect account');

      const result = (await response.json()) as IStripeConnectAccountResponse;

      // Save the account ID to user data
      if (result.success && result.accountId) {
        await updateUserData({
          stripeConnectAccountId: result.accountId,
        });
      }

      return result;
    },
  });

  // Mutation to create account link for onboarding/updates
  const createAccountLink = useMutation({
    mutationFn: async () => {
      const user = await account.get();

      const userData = await getUserData();

      const response = await fetch(
        `${Env.STRIPE_API_URL}/create-stripe-connect-account-link`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-appwrite-user-id': user.$id,
          },
          body: JSON.stringify({
            stripeConnectAccountId: userData?.stripeConnectAccountId,
            refreshUrl: `${Env.STRIPE_API_URL}/wallet/connect-account`, // URL to redirect if link expires
            returnUrl: `${Env.STRIPE_API_URL}/wallet`, // URL to redirect after completion
          }),
        }
      );

      if (!response.ok) throw new Error('Failed to create account link');
      return response.json() as Promise<{
        success: boolean;
        accountLink: string;
      }>;
    },
  });

  // Mutation to get Stripe Connect account details
  const getStripeConnectAccount = useMutation({
    mutationFn: async () => {
      const user = await account.get();

      // Get the account ID from user data
      const userData = await getUserData();
      const accountId = userData?.stripeConnectAccountId;

      if (!accountId) {
        throw new Error('No Stripe Connect account ID found in user data');
      }

      const response = await fetch(
        `${Env.STRIPE_API_URL}/get-stripe-connect-account`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-appwrite-user-id': user.$id,
          },
          body: JSON.stringify({
            stripeConnectAccountId: accountId,
          }),
        }
      );

      if (!response.ok) throw new Error('Failed to get Stripe Connect account');
      return response.json() as Promise<IStripeConnectAccountResponse>;
    },
  });

  return {
    createConnectAccount,
    createAccountLink,
    getStripeConnectAccount,
  };
};
