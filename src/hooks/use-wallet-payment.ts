import { Env } from '@env';
import { useStripe } from '@stripe/stripe-react-native';
import { useMutation, useQuery } from '@tanstack/react-query';

import { account } from '@/lib/appwrite';

interface CreatePaymentIntentResponse {
  ephemeralKey: string;
  paymentIntent: string;
}

export const useWalletPayment = () => {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  // Query to get or create Stripe customer
  const { data: customer } = useQuery({
    queryKey: ['stripe-customer'],
    queryFn: async () => {
      console.log('useWalletPayment querying customer');
      const user = await account.get();

      console.log('useWalletPayment customer', user);
      if (!user?.phone) throw new Error('User phone number is required');

      const response = await fetch(`${Env.STRIPE_API_URL}/customer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: user.name,
          email: user.email,
          phone: user.phone,
        }),
      });
      console.log('useWalletPayment response', response);
      if (!response.ok) {
        console.log('useWalletPayment failed to get customer', response);
        throw new Error('Failed to get customer');
      }

      const { customer } = await response.json();
      console.log('useWalletPayment customer from api', customer);
      return customer;
    },
    // enabled: !!user?.phone,
  });

  // Mutation to create payment intent
  const createPaymentIntent = useMutation({
    mutationFn: async (amount: number) => {
      const user = await account.get();
      if (!customer?.id) throw new Error('Customer ID is required');

      console.log('x-appwrite-user-id header', user.$id);

      const response = await fetch(
        `${Env.STRIPE_API_URL}/create-payment-intent`,
        {
          method: 'POST',
          headers: {
            'x-appwrite-user-id': user.$id,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: user.$id,
            amount,
            customerId: customer.id,
            currency: 'usd',
          }),
        }
      );

      if (!response.ok) throw new Error('Failed to create payment intent');

      const data: CreatePaymentIntentResponse = await response.json();
      return data;
    },
  });

  // Mutation to initialize and present payment sheet
  const processPayment = useMutation({
    mutationFn: async (amount: number) => {
      const user = await account.get();
      // Create payment intent
      const { ephemeralKey, paymentIntent } =
        await createPaymentIntent.mutateAsync(amount);

      // Initialize payment sheet
      const { error: initError } = await initPaymentSheet({
        merchantDisplayName: 'TheDal',
        customerId: customer?.id,
        paymentIntentClientSecret: paymentIntent,
        customerEphemeralKeySecret: ephemeralKey,
        defaultBillingDetails: {
          phone: user?.phone,
        },
      });

      if (initError) throw initError;

      // Present payment sheet
      const { error: presentError } = await presentPaymentSheet();
      if (presentError) throw presentError;

      return true;
    },
  });

  return {
    customer,
    createPaymentIntent,
    processPayment,
  };
};
