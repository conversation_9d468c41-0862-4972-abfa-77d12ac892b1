import { View, Text, Pressable } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';

type IBackButton = {
  onPress: () => void;
  onPress3?: () => void;
  text?: string;
  text2?: string;
  text3?: string;
};

const BackButton: React.FC<IBackButton> = ({
  onPress,
  text,
  text2,
  text3,
  onPress3,
}) => {
  return (
    <View className="pt-safe flex-row items-center justify-between">
      {/* Back Button */}
      <Pressable onPress={onPress} className="flex-row items-center gap-2">
        <Image
          source={require('../../assets/images/back-icon.svg')}
          style={{ height: 12, width: 6 }}
          tintColor={'white'}
          contentFit="contain"
        />
        {text && (
          <Text className="text-sm font-medium text-black-50 dark:text-white">
            {text}
          </Text>
        )}
      </Pressable>

      {/* Optional Centered Text */}
      {text2 && (
        <View className="flex-1 items-center justify-center">
          <Text
            className="font-PoppinsBold text-primary-750 text-xl dark:text-black-0 font-bold"
            style={{ textAlign: 'center' }}
          >
            {text2}
          </Text>
        </View>
      )}

      {/* Optional Third Text with Action */}
      {text3 && (
        <Pressable onPress={onPress3}>
          <Text className="text-primary-750 dark:text-black-100 text-sm font-inter font-medium">
            {text3}
          </Text>
        </Pressable>
      )}
    </View>
  );
};

export default BackButton;
