import { View, Text, Image } from 'react-native';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';

const CompanionCard = () => {
  return (
    <SafeAreaView className='flex-1'>
      <View className="  px-4 py-4 rounded-[12px]  bg-black-950 border border-secondary-650 gap-4 mt-4  dark:bg-bgdark ">
        <View className="flex-row  justify-between items-center">
          <View className="flex-row items-center gap-4">
            <Image
              source={require('../../assets/images/damy-image.png')}
              resizeMode="cover"
              style={{ height: 48, width: 48 }}
            />
            <View className="gap-4">
              <Text className="text-black-0 font-bold text-[17px] font-PoppinsBold dark:text-secondary-700">
                Kaushiki
              </Text>
              <Text className="  text-black-0 font-normal leading-5 font-inter dark:text-black-50 ">
                male, 23yrs, Indian
              </Text>
            </View>
          </View>

          <View className="gap-4">
            <Text className="text-black-950 font-bold font-inter leading-5 dark:text-black-0">
              Rating
            </Text>
            <Text className="text-black-950 leading-5 font-inter dark:text-black-50">
              Rated by: 56
            </Text>
          </View>
        </View>
        <View className="border border-borderdark"></View>

        <View className="flex-row  items-center gap-2">
          <Image
            source={require('../../assets/images/text-icon.png')}
            resizeMode="contain"
            style={{ height: 24, width: 24 }}
          />

          <View className="px-2 py-[5px] bg-black-0 rounded-[20px] dark:bg-secondary-300">
            <Text className="text-black-950 font-inter leading-4 text-[12px] font-normal  dark:text-secondary-700">
              Punjabi
            </Text>
          </View>
          {/* rounded-14 px and bg-secondary is not working */}
          <View className="px-2 py-[5px] bg-black-0 rounded-[20px] dark:bg-secondary-300">
            <Text className="text-black-950 font-inter text-[12px] leading-4  dark:text-secondary-700">
              Punjabi
            </Text>
          </View>
          <Text className="text-black-950 font-inter font-normal text-[12px] leading-4 dark:text-secondary-700">
            +5More
          </Text>
        </View>

        <View className="flex-row items-center gap-2">
          <Image
            source={require('../../assets/images/genderpreference.png')}
            resizeMode="contain"
            style={{ height: 24, width: 24 }}
          />
          <Text className="text-black-950 font-inter leading-4 text-[12px] dark:text-black-0">
            Male
          </Text>
        </View>
        <View className="flex-row gap-2">
          <View className="px-[10px] py-[6px] rounded-[8px] flex-row items-center gap-2 dark:bg-black-0">
            <Image
              source={require('../../assets/images/plane.png')}
              resizeMode="contain"
              style={{ height: 8.999, width: 9 }}
              tintColor={'#071952'}
            />
            <Text className="font-inter text-black-950 leading-[14px] text-[11px] font-normal  dark:text-blue">
              Solo Traveler
            </Text>
          </View>
          <View className="px-[10px] py-[6px] rounded-[8px] dark:bg-black-0">
            <Text className="text-black-0 text-[11px] leading-[14px] font-normal dark:text-blue ">
              Assistance: 45 Trips{' '}
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CompanionCard;
