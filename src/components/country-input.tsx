import React, { useState } from 'react';
import { Text, View } from 'react-native';
import PhoneInput, {
  type ICountry,
} from 'react-native-international-phone-number';

export default function App() {
  const [selectedCountry, setSelectedCountry] = useState<null | ICountry>(null);
  const [inputValue, setInputValue] = useState<string>('');

  function handleInputValue(phoneNumber: string) {
    setInputValue(phoneNumber);
  }

  function handleSelectedCountry(country: ICountry) {
    setSelectedCountry(country);
  }

  return (
    <View style={{ width: '100%' }}>
      <Text className="mb-1 font-inter text-black-900 dark:text-black-0">
        phone Number
      </Text>
      <PhoneInput
        placeholder="Enter Your phone number"
        value={inputValue}
        onChangePhoneNumber={handleInputValue}
        selectedCountry={selectedCountry}
        onChangeSelectedCountry={handleSelectedCountry}
        phoneInputStyles={{
          container: {
            backgroundColor: '#FDFDFD',
            borderWidth: 1,
            borderStyle: 'solid',
            borderColor: '#E6E6E6',
            borderRadius: 12,
            height: 55,
            // paddingVertical:14
          },
          flagContainer: {
            backgroundColor: '#FDFDFD dark',
            justifyContent: 'center',
            borderTopLeftRadius: 12,
            borderBottomLeftRadius: 12,
          },
        }}
      />
    </View>
  );
}
