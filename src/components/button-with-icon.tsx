import { View, Text, SafeAreaView, Pressable } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';

type IButtonWithIcon = {
  onPress?: () => void;
};
const ButtonWithIcon: React.FC<IButtonWithIcon> = ({ onPress }) => {
  return (
    <SafeAreaView className="">
      <Pressable
        onPress={onPress}
        className="px-10 py-[14px] rounded-[12px] border border-skeletoncard flex-row items-center"
      >
        <Image
          source={require('../../assets/images/user-text-input.svg')}
          contentFit="contain"
          style={{ width: 16, height: 19.178 }}
        />
        <View className="flex-1 items-center">
          <Text className="text-[16px] font-medium leading-6 font-inter dark:text-black-50">
            Login {/*as Guest*/}
          </Text>
        </View>
      </Pressable>
    </SafeAreaView>
  );
};
export default ButtonWithIcon;
