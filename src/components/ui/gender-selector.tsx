import React from 'react';
import { Pressable, Text, View } from 'react-native';

import { cn } from '@/lib';

const GenderSelector = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  return (
    <View className="mt-2.5 flex-row justify-between  rounded-[12px] border border-borderdark bg-black-0 px-5 py-[14px] text-black-0 dark:bg-bgtextInput">
      <Pressable onPress={() => onChange('MALE')}>
        <View
          className={cn('flex-row gap-2 items-center px-2 py-2 ', {
            ' bg-black-0 dark:bg-primary-900 rounded-[12px] text-black-0':
              value === 'MALE',
          })}
        >
          <Text
            className={cn(
              'text-black-0 dark:text-black-300',
              value === 'MALE' ? 'dark:text-black-200 ' : 'text-black-300'
            )}
          >
            Male
          </Text>
        </View>
      </Pressable>

      <Pressable onPress={() => onChange('FEMALE')}>
        <View
          className={cn('flex-row gap-2 items-center px-2 py-2 ', {
            ' bg-black-0 dark:bg-primary-900 rounded-[12px]':
              value === 'FEMALE',
          })}
        >
          <Text
            className={cn(
              'text-black-0 dark:text-black-300',
              value === 'FEMALE' ? 'dark:text-black-200' : 'text-black-300'
            )}
          >
            Female
          </Text>
        </View>
      </Pressable>
      <Pressable onPress={() => onChange('OTHERS')}>
        <View
          className={cn('flex-row gap-2 items-center px-2 py-2 ', {
            'bg-blue dark:bg-primary-900 rounded-[12px] text-black-0':
              value === 'OTHERS',
          })}
        >
          <Text
            className={cn(
              'dark : text-black-300',
              value === 'OTHERS' ? 'dark:text-black-200' : 'text-black-300'
            )}
          >
            Others
          </Text>
        </View>
      </Pressable>
    </View>
  );
};

export default GenderSelector;
