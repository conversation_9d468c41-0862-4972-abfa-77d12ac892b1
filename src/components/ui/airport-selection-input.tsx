import React from 'react';
import { FlatList, Image, Pressable, Text, View } from 'react-native';
import { useDebouncedCallback } from 'use-debounce';

import InputText from '@/components/input-txt';
import { Button } from '@/components/ui/button';
import { Modal, useModal } from '@/components/ui/modal';
import { cn } from '@/lib/utils';
import { type Airport, useAirportStore } from '@/store/airport-store';

// Consider creating proper interfaces
interface AirportSelectionInputProps {
  index: number;
  airport?: Flight;
  setAirport: (flight: Flight) => void;
  removeAirport: (index: number) => void;
  totalFlights: number;
}

interface Flight {
  index: number;
  id: string;
  name: string;
}

const MODAL_SNAP_POINTS = ['95%'];
const SEARCH_PLACEHOLDER = 'Search By Airport Name / IATA Code / City';
const ICON_DIMENSIONS = { height: 16, width: 16 };

const AirportSelectionInput = ({
  index,
  airport,
  setAirport,
  removeAirport,
  totalFlights,
}: AirportSelectionInputProps) => {
  const { ref, present, dismiss } = useModal();
  const { airports, isLoading, fetchAirports } = useAirportStore();
  const [filterAirport, setFilterAirport] = React.useState('');

  const debouncedSetFilter = useDebouncedCallback(
    (value: string) => setFilterAirport(value),
    300
  );

  // Add memoization for filtered airports
  const filteredAirports = React.useMemo(
    () =>
      filterAirport
        ? airports.filter(
            (a) =>
              a.name.toLowerCase().includes(filterAirport.toLowerCase()) ||
              a.shortCode.toLowerCase().includes(filterAirport.toLowerCase()) ||
              a.airportLocation
                .toLowerCase()
                .includes(filterAirport.toLowerCase())
          )
        : airports,
    [airports, filterAirport]
  );

  const handleAirportSelect = (item: Airport) => {
    const newSelectedAirport = {
      index: index,
      id: item.id,
      name: item.name,
    };

    setAirport(newSelectedAirport);
    dismiss();
  };

  // Memoize the airport item component
  const AirportItem = React.memo(
    ({
      item,
      onSelect,
    }: {
      item: Airport;
      onSelect: (item: Airport) => void;
    }) => (
      <Pressable onPress={() => onSelect(item)}>
        <Text className="p-3 text-white">{item.name}</Text>
      </Pressable>
    )
  );

  React.useEffect(() => {
    if (airports.length === 0) {
      fetchAirports();
    }
  }, []);

  return (
    <View
      className={cn(
        'flex-1 flex-row items-center justify-between border bg-bgtextInput px-5  border-customborder border-b-0',
        index === 0 && 'rounded-t-xl',
        index === totalFlights - 1 && 'rounded-b-xl border-b-1'
      )}
    >
      <Pressable
        onPress={() => {
          present();
        }}
        className="flex-1"
      >
        <View
          key={airport?.id ?? index}
          // style={{ backgroundColor: 'red' }}
          className="w-full flex-1 flex-row py-[14px]"
        >
          <View className="flex-1 flex-row items-center gap-2">
            <Image
              source={require('../../../assets/images/location.png')}
              resizeMode="contain"
              style={{ width: 12, height: 15 }}
            />
            <Text className="flex-1 text-[14px] font-normal leading-5 text-black-300">
              {airport?.name ?? 'Select Airport'}
            </Text>
          </View>
          {index !== 0 && (index !== totalFlights - 1 || totalFlights > 2) && (
            <Pressable
              onPress={() => {
                removeAirport(airport?.index ?? index);
              }}
              className="absolute right-0 top-3"
            >
              <View className="">
                <Image
                  source={require('../../../assets/images/circlecross.png')}
                  resizeMode="contain"
                  style={{ width: 20, height: 20 }}
                />
              </View>
            </Pressable>
          )}
        </View>
      </Pressable>
      <Modal
        ref={ref}
        snapPoints={MODAL_SNAP_POINTS}
        onDismiss={dismiss}
        backgroundStyle={{ backgroundColor: '#020717' }}
      >
        <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
          {/* <View className="flex-row items-center justify-between">
            <Pressable
              onPress={() => {
                dismiss();
              }}
            >
              <Image
                source={require('../../../assets/images/cross.png')}
                contentFit="contain"
                style={{ height: 28, width: 28 }}
              />
            </Pressable>
            <View className="rounded-[12px] bg-black-0  px-[27px] py-3 dark:bg-primary-900 ">
              <Text className="font-inter text-[16px] font-normal text-black-950 dark:text-black-0">
                Save
              </Text>
            </View>
          </View> */}
          <View className="flex-1 justify-between">
            <View className="mb-3 mt-6 flex-row items-center">
              <InputText
                onPress={() => {}}
                placeholder={SEARCH_PLACEHOLDER}
                className="flex-1 dark:bg-blue"
                iconSourceFirst={require('../../../assets/images/search-icon.png')}
                iconStyleFirst={ICON_DIMENSIONS}
                // iconSourceSecond={require('../../../assets/images/cross.png')}
                iconStyleSecond={ICON_DIMENSIONS}
                onChangeText={debouncedSetFilter}
              />
              {/* <Pressable>
                <Text className="dark: ml-2 font-inter font-normal leading-6 text-black-50">
                  Cancel
                </Text>
              </Pressable> */}
            </View>
            {isLoading ? (
              <View className="flex-1 items-center justify-center">
                <Text className="text-white">Loading airports...</Text>
              </View>
            ) : (
              <FlatList
                data={filteredAirports}
                renderItem={({ item }) => (
                  <AirportItem item={item} onSelect={handleAirportSelect} />
                )}
                keyExtractor={(item) => item.id}
              />
            )}
            <View className="mb-4">
              <Button variant="secondary" label="Save" onPress={() => {}} />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default AirportSelectionInput;
