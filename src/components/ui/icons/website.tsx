import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { ClipPath, Defs, G, Path } from 'react-native-svg';

import colors from '../colors';

export const Website = ({
  color = colors.neutral[500],
  ...props
}: SvgProps) => (
  <Svg width={24} height={24} fill="none" viewBox="0 0 24 24" {...props}>
    <G
      clipPath="url(#a)"
      stroke={color}
      strokeWidth={1.219}
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <Path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18ZM3 12h18" />
      <Path d="M12 20.756c2.07 0 3.75-3.92 3.75-8.756S14.07 3.244 12 3.244c-2.071 0-3.75 3.92-3.75 8.756s1.679 8.756 3.75 8.756Z" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h24v24H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
