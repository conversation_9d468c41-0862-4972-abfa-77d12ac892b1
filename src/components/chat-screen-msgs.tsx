import React from 'react';
import { Image, Pressable, Text, View } from 'react-native';

type PropsType = {
  message: string;
  isTrue: boolean;
  time: string;
  unread: boolean;
};

export function LeftMsg({ message, isTrue, time, unread }: PropsType) {
  return (
    <View className="my-1 w-auto max-w-[85%] self-start rounded-t-[10px] rounded-br-[10px] bg-chatcardbg p-3">
      <Text className="font-inter text-sm font-normal text-blue">
        {message}
      </Text>
      {isTrue ? (
        <Pressable>
          <Text className="font-inter text-sm font-bold text-[#003F28]">
            Read more
          </Text>
        </Pressable>
      ) : null}
      <View className="flex-row items-center justify-end gap-1">
        <Text
          className={`font-inter text-[10px] font-medium ${unread ? 'text-[#999999]' : 'text-secondary-650'}`}
        >
          {time}
        </Text>
        <Image
          source={require('../../assets/images/checkcheck.png')}
          className="size-2.5"
          tintColor={unread ? '#999999' : '#12725B'}
        />
      </View>
    </View>
  );
}

export function RightMsg({ message, isTrue, time, unread }: PropsType) {
  return (
    <View className="my-4 w-auto max-w-[85%] items-end self-end rounded-b-[10px] rounded-tl-[10px] bg-primary-900 p-3">
      <Text className="font-inter text-sm font-normal text-black-50">
        {message}
      </Text>
      {isTrue ? (
        <Pressable>
          <Text className="font-inter text-sm font-bold text-[#003F28]">
            Read more
          </Text>
        </Pressable>
      ) : null}
      <View className="flex-row items-center justify-end gap-1">
        <Text
          className={`font-inter text-[10px] font-medium ${unread ? 'text-[#999999]' : 'text-secondary-650'}`}
        >
          {time}
        </Text>
        <Image
          source={require('../../assets/images/checkcheck.png')}
          className="size-2.5"
          tintColor={unread ? '#999999' : '#12725B'}
        />
      </View>
    </View>
  );
}
