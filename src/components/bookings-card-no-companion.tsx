import { Env } from '@env';
import { format } from 'date-fns';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import { Pressable, Text, View } from 'react-native';

import { databases } from '@/lib/appwrite';

type IBookingsCardProps = {
  bookingId: string;
  onPress?: () => void;
};
const BookingsCardNoCompanion: React.FC<IBookingsCardProps> = ({
  bookingId,
  onPress,
}) => {
  const [bookingDetails, setBookingDetails] = useState<{
    flightPNR: string;
    bookingDestinations: string[];
    flightCompany: string;
    flightTime: string;
    travellersPhoto: string;
    passportPhoto: string;
    travellersName: string;
    status: 'PENDING' | 'ACTIVE' | 'CONFIRMED';
  } | null>(null);

  const getBookingDetails = async () => {
    // Fetch booking details using bookingId
    // Update bookingDetails state with fetched data
    const bookingDetails = await databases.getDocument(
      Env.COLLECTION_ID,
      Env.COLLECTION_BOOKING_FLIGHT_DETAILS,
      bookingId
    );
    console.log('booking details are', bookingDetails);
    setBookingDetails(bookingDetails);
  };

  useEffect(() => {
    // Fetch booking details using bookingId
    // Update bookingDetails state with fetched data
    getBookingDetails();
  }, [bookingId]);

  if (!bookingDetails) {
    return null; // Render a loading state or placeholder while fetching data
  }

  const totalDestinations = bookingDetails.bookingDestinations.length;

  return (
    <Pressable
      className="my-3 gap-4 rounded-lg bg-custombg px-2.5 py-3"
      onPress={onPress}
    >
      <View className="gap-3">
        <Text className="text-center font-PoppinsBold text-lg font-bold leading-7 dark:text-black-50  ">
          {format(bookingDetails.flightTime, 'dd MMM yyyy')}
        </Text>

        <View className="gap-1">
          <View className="flex-row items-center justify-between">
            <Text className="font-inter text-base font-bold  dark:text-black-50">
              {bookingDetails.bookingDestinations[0].airports.shortCode}
            </Text>

            <View className="flex-row items-center">
              <Image
                source={require('../../assets/images/circle.svg')}
                style={{ width: 18, height: 18 }}
                contentFit="contain"
              />

              <View className="w-[60px] border border-dashed  border-black-0 "></View>
              <Image
                source={require('../../assets/images/lineplane.svg')}
                contentFit="contain"
                style={{ height: 19, width: 18 }}
                className="absolute left-11"
              />
              <Image
                source={require('../../assets/images/circle.svg')}
                style={{ width: 18, height: 18 }}
                contentFit="contain"
              />
            </View>
            <Text className="font-inter text-base font-bold  dark:text-black-50">
              {
                bookingDetails.bookingDestinations[totalDestinations - 1]
                  .airports.shortCode
              }
            </Text>
          </View>

          <View className="flex-row items-center justify-between gap-1">
            <View className="flex-row gap-1">
              <Image
                source={require('../../assets/images/flight-take-off.svg')}
                style={{ height: 16, width: 16 }}
                contentFit="contain"
              />
              <Text className="font-inter text-sm  font-medium dark:text-black-100">
                {format(bookingDetails.flightTime, 'hh:mm a')}
              </Text>
            </View>
            <Text className="font-inter text-sm  font-medium dark:text-black-100">
              {/* {'travelTiming'} */}
            </Text>
            <View className="right-0 flex-row items-center gap-1">
              {/* <Image
                source={require('../../assets/images/flight-take-in.svg')}
                style={{ height: 16, width: 16 }}
                contentFit="contain"
              /> */}
              <Text className="right-0 text-right  font-inter text-sm font-medium dark:text-black-100">
                {/* {'alightingTiming'} */}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* <View className="w-full  flex-row justify-between ">
        <View className="flex-row  gap-2">
          <Image
            source={profileImage}
            contentFit="contain"
            style={{ width: 50, height: 50 }}
          />
          <View className="gap-1">
            <Text className="text-base  font-bold  text-green dark:text-green">
              {TravelerName}
            </Text>
            <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-0">
              {TravelerDetails}
            </Text>
          </View>
        </View>

        <View className="items-center gap-1">
          <Text
            className={cn(
              'font-inter text-base font-bold dark:text-black-100',
              RuppeColor
            )}
          >
            $ 50.00
          </Text>

          <View
            className={cn(
              'flex-row items-center px-2.5 py-1.5 rounded-lg dark:bg-black-50',
              className
            )}
          >
            <Text
              className={cn(
                'font-PoppinsMedium text-[11px] font-medium dark:text-blue',
                classNameText
              )}
            >
              {text}
            </Text>
          </View>
        </View>
      </View> */}
    </Pressable>
  );
};

export default BookingsCardNoCompanion;
