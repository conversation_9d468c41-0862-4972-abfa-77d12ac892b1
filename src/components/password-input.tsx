import { cn } from '@/lib/utils';
import { FC, useState } from 'react';
import {
  TextInput,
  TextInputProps,
  View,
  Image,
  ImageSourcePropType,
  ImageStyle,
  Pressable,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import InputErrorMsg from './input-error-msg';
import { t } from 'i18next';
import { FieldError } from 'react-hook-form';
import { useColorScheme } from "nativewind";

export interface IInputText extends TextInputProps {
  placeholder: string;
  error?: FieldError;
}

const PasswordInput: FC<IInputText> = ({
  placeholder,
  className,
  placeholderTextColor,
  error,

  ...props
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };
  const { colorScheme } = useColorScheme();
  const defaultPlaceholderColor = colorScheme === 'dark' ? '#B3B3B3' : 'black';




  return (
    <>
      <View
        className={cn(
          'rounded-[12px] mt-2 flex-row items-center  py-[14px] px-5 border border-black-100 justify-between dark:bg-bgtextInput border-customborder ',
          className,
          error
        )}
      >
        <View className="flex-row gap-2 items-center">
          <TextInput
            className={cn('text-black-950   dark:text-black-0 ')}
            placeholderTextColor={placeholderTextColor || defaultPlaceholderColor}
            secureTextEntry={!isPasswordVisible}
            placeholder={placeholder}
            {...props}
          />
        </View>
        <Pressable onPress={togglePasswordVisibility}>
          <Image
            source={
              isPasswordVisible
                ? require('../../assets/images/eyeon.png')
                : require('../../assets/images/eyeoff.png')
            }
            resizeMode="contain"
            style={{ height: 20, width: 20 }}
          />
        </Pressable>
      </View>

      {error?.message && (
        <InputErrorMsg message={t(error.message)} className="mt-2" />
      )}
    </>
  );
};

PasswordInput.displayName = 'PasswordInput';

export default PasswordInput;
