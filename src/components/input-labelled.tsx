import {FC, ReactNode} from 'react';
import {View, Text, ViewProps} from 'react-native';

export interface IInputLabelled extends ViewProps {
  label: string;
  children: ReactNode;
}

const InputLabelled: FC<IInputLabelled> = ({
  label,
  children,
  className,
  ...props
}) => {
  return (
    <View className={className} {...props}>
      <Text className="font-PoppinsMedium font-medium text-[16px] leading-6 text-black-950 dark:text-black-50">
        {label}
      </Text>
      {children}
    </View>
  );
};

InputLabelled.displayName = 'InputLabelled';

export default InputLabelled;
