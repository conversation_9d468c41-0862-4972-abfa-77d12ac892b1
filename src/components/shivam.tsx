import { View, Text, Pressable } from 'react-native';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { AntDesign } from '@expo/vector-icons';

type IButton = {
  onPress:() =>void;
  icon?:React.ReactNode;
  className:string
};

const Shivam :React.FC<IButton>= ({onPress,icon,className}) => {
  return (
    <Pressable onPress={onPress}>
      <View className=" py-[14px] bg-blue items-center w-full rounded-[12px]">
        <View className="flex-row items-center gap-2">
          <Text className="text-black-0 font-PoppinsBold font-bold dark:text-black-900 text-base">
            Get Started
          </Text>
          {/* <AntDesign name="right" size={20} color="white" /> */}
          {icon ? icon : <AntDesign name="right" size={20} color="white" />}
        </View>
      </View>
    </Pressable>
  );
};

export default Shivam;
