import { cn } from '@/lib/utils';
import { FC } from 'react';
import {
  TextInput,
  TextInputProps,
  View,
  ImageSourcePropType,
  ImageStyle,
  useColorScheme,
  Pressable,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import InputErrorMsg from './input-error-msg';
import { t } from 'i18next';
import { FieldError } from 'react-hook-form';
import { Image } from 'expo-image';

export interface IInputText extends TextInputProps {
  placeholder: string;
  error?: FieldError;
  iconSourceFirst?: ImageSourcePropType;
  iconStyleFirst?: ImageStyle;
  iconSourceSecond?: ImageSourcePropType;
  iconStyleSecond?: ImageStyle;
  disabled?: boolean;
  onPress?: () => void;
  onPress2icon?: () => void;
}

const InputText: FC<IInputText> = ({
  placeholder,
  className,
  placeholderTextColor,
  error,
  iconSourceFirst,
  iconStyleFirst,
  iconSourceSecond,
  iconStyleSecond,
  disabled = false,

  onPress,
  onPress2icon,
  ...props
}) => {
  const colorScheme = useColorScheme();

  // Set placeholder text color based on the current color scheme
  const defaultPlaceholderColor = colorScheme === 'dark' ? '#B3B3B3' : 'black';

  return (
    <>
      <View
        className={cn(
          'rounded-[12px] mt-2 flex-row items-center dark:bg-bgtextInput border border-customborder  ',
          className,
          error && 'border-red-500',
        )}
      >
        <Pressable
          className="flex-row flex-1 gap-2 items-center"
          onPress={onPress}
        >
          {iconSourceFirst && (
            <TouchableOpacity onPress={onPress} className="py-3.5 pl-4 ">
              <Image
                source={iconSourceFirst}
                contentFit="contain"
                style={iconStyleFirst}
              />
            </TouchableOpacity>
          )}
          <TextInput
            caretHidden={false}
            className={cn(
              `text-black-950 flex-1 py-3.5 ${iconSourceFirst ? 'pl-1' : 'pl-5'} dark:text-black-100 `,
            )}
            placeholderTextColor={
              placeholderTextColor || defaultPlaceholderColor
            }
            placeholder={placeholder}
            editable={!disabled}
            onPress={onPress}
            {...props}
          />

          {iconSourceSecond && (
            <TouchableOpacity onPress={onPress2icon} className="py-3.5 px-4 ">
              <Image
                source={iconSourceSecond}
                contentFit="contain"
                style={iconStyleSecond}
              />
            </TouchableOpacity>
          )}
        </Pressable>
      </View>

      {error?.message && (
        <InputErrorMsg message={t(error.message)} className="mt-2" />
      )}
    </>
  );
};

InputText.displayName = 'InputText';

export default InputText;
