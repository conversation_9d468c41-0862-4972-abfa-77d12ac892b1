import { Image } from 'expo-image';
import React, { useEffect } from 'react';
import { Text, TouchableOpacity } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { cn } from '@/lib/utils';

// Define a type for the route names (keys of the images object)
type RouteName = 'home' | 'match' | 'chat' | 'offers' | 'profile';

type IBottomTabButton = {
  isFocused: boolean;
  label: string;
  routeName: RouteName; // Use RouteName type here instead of string
  color: string;
  onPress: () => void;
};

const BottomTabButton: React.FC<IBottomTabButton> = (props) => {
  const { isFocused, routeName, onPress, label } = props;
  const scale = useSharedValue(0);

  useEffect(() => {
    scale.value = withSpring(isFocused ? 1 : 0, {
      duration: 350,
    });
  }, [scale, isFocused]);

  const animatedImageStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  // Type the images object with the specific keys (RouteName)
  const images: Record<RouteName, any> = {
    home: require('../../assets/images/home.svg'),
    match: require('../../assets/images/match.png'),
    chat: require('../../assets/images/chat.svg'),
    offers: require('../../assets/images/offers.svg'),
    profile: require('../../assets/images/user-text-input.svg'),
  };

  return (
    <TouchableOpacity
      className="flex-1 flex-col items-center justify-center gap-[2px]"
      onPress={onPress}
    >
      <Animated.View style={[animatedImageStyle]}>
        <Image
          source={images[routeName]}
          contentFit="contain"
          style={{ height: 24, width: 24 }}
        />
      </Animated.View>

      <Text
        className={cn(
          'font-PoppinsMedium text-xs font-medium leading-4',
          isFocused ? 'text-black-50' : 'text-black-600',
        )}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
};

export default BottomTabButton;
