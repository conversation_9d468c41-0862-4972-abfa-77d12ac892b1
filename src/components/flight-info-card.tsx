import { View, Text } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';

const FlightInfoCard = () => {
  return (
    <View>
      <View className="px-3 py-4 rounded-[8px] gap-[36px] bg-darkcardbg dark:bg-primary-950 border border-black-0 "
      >
        <View className="gap-3">
          <Text className="font-PoppinsBold text-[18px] font-bold text-center leading-7 dark:text-black-50  ">
            19 June , 2024
          </Text>

          <View className="gap-1">
            <View className="flex-row items-center justify-between">
              <Text className="font-inter text-[16px] font-bold leading-[24px] dark:text-black-50">
                LGA
              </Text>
              
              <View className='flex-row items-center'>
                <Image source={require("../../assets/images/circle.svg")} style={{ width: 18, height: 18 }} contentFit='contain' />
                <View className='border border-dashed w-[60px]  border-black-0 '></View>
                <Image source={require("../../assets/images/lineplane.svg")} contentFit='contain' style={{ height: 19, width: 18 }} className='absolute left-11' />
                <Image source={require("../../assets/images/circle.svg")} style={{ width: 18, height: 18 }} contentFit='contain' />

              </View>
              
              <Text className="font-inter text-[16px] font-bold leading-[24px] dark:text-black-50">
                LGA
              </Text>
            </View>

            <View className="gap-1 flex-row items-center justify-between">
              <View className="flex-row gap-1">
                <Image
                  //   source={require('../../../assets/images/flight-take-off.svg')}
                  source={require('../../assets/images/flight-take-off.svg')}
                  style={{ height: 16, width: 16 }}
                  contentFit="contain"
                />
                <Text className="font-inter text-[12px] leading-4 font-normal dark:text-black-100">
                  10:00 AM
                </Text>
              </View>
              <Text className="font-inter text-[12px] leading-4 font-normal dark:text-black-100">
                10:00 AM
              </Text>
              <View className="flex-row items-center right-0 gap-1">
                <Image
                  //   source={require('../../../assets/images/flight-take-off.svg')}
                  source={require('../../assets/images/flight-take-in.svg')}
                  style={{ height: 16, width: 16 }}
                  contentFit="contain"
                />
                <Text className="font-inter text-[12px] leading-4 font-normal right-0 text-right dark:text-black-100">
                  10:00 AM
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* <View className="flex-row  justify-between w-full ">
          <View className="flex-row  gap-2">
            <Image
              //   source={require('../../../assets/images/profile.png')}
              source={require('../../assets/images/profile.png')}
              contentFit="contain"
              style={{ width: 50, height: 50 }}
            />
            <View className="gap-1">
              <Text className="text-green  font-bold leading-6 text-[16px] dark:text-green">
                Kaushiki
              </Text>
              <Text className="text-black-950 font-normal leading-5 text-[14px] dark:text-black-0">
                Female, 25yrs, Indian
              </Text>
            </View>
          </View>

          <View className="gap-1">
            <View className="flex-row items-center  gap-1">
              <Text className="text-black-0">Rating start </Text>
              <Text className="text-black-0 font-bold">58</Text>
            </View>

            <View className="flex-row items-center px-[10px] py-[6px] rounded-[8px] dark:bg-black-50">
              <Image
                source={require('../../assets/images/plane.png')}
                contentFit="contain"
                style={{ height: 12, width: 12 }}
                tintColor={'#071952'}
              />
              <Text className="font-inter text-[11px] font-normal leading-[14px] dark:text-blue">
                Solo Traveler
              </Text>
            </View>
          </View>
        </View> */}

      </View>
    </View>
  );
};

export default FlightInfoCard;
