import { View, Text, ImageSourcePropType } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import { cn } from '@/lib/utils';
type ITransactionsContainerProps = {
  text: string;
  price: string;
  profileImage: ImageSourcePropType;
  arrowImage: ImageSourcePropType;
  className?: string;
  classNameText?: string;
};
const TransactionsContainer: React.FC<ITransactionsContainerProps> = ({
  text,
  price,
  profileImage,
  arrowImage,
  className,
  classNameText,
}) => {
  return (
    <View
      className={cn(
        'mt-3 px-3 pt-2 pb-2.5 bg-black-50 border-0.5 border-b-green border-t-primary-950 border-l-primary-950 flex-row items-center justify-between dark:bg-primary-950',
      )}
    >
      <View className="flex-1 flex-row items-center gap-2">
        <View className="rounded-[10px]">
          <Image
            source={profileImage}
            contentFit="contain"
            style={{ height: 32, width: 32 }}
          />
        </View>
        <Text className="font-inter text-sm font-normal text-black-950 dark:text-black-300 text-wrap">
          {text}
        </Text>
      </View>

      <View
        className={cn(
          'px-1 py-1 rounded-md dark: bg-chatcardbg items-center justify-center flex-row gap-1 ',
          className,
        )}
      >
        <Image
          source={arrowImage}
          contentFit="contain"
          style={{ height: 20, width: 20 }}
        />

        <Text
          className={cn(
            'font-inter text-base text-black-0 dark:text-secondary-650',
            classNameText,
          )}
        >
          {price}
        </Text>
      </View>
    </View>
  );
};

export default TransactionsContainer;
