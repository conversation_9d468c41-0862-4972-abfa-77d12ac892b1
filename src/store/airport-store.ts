import { Env } from '@env';
import { Query } from 'react-native-appwrite';
import { create } from 'zustand';

import { databases } from '@/lib/appwrite';

export const AIRLINES = [
  { name: 'American Airlines', iata: 'AA' },
  { name: 'Delta Air Lines', iata: 'DL' },
  { name: 'United Airlines', iata: 'UA' },
  { name: 'Southwest Airlines', iata: 'WN' },
  { name: 'Lufthansa', iata: 'LH' },
  { name: 'British Airways', iata: 'BA' },
  { name: 'Air France', iata: 'AF' },
  { name: 'Emirates', iata: 'EK' },
  { name: 'Qatar Airways', iata: 'QR' },
  { name: 'Singapore Airlines', iata: 'SQ' },
  { name: 'Turkish Airlines', iata: 'TK' },
  { name: 'Air India', iata: 'AI' },
  { name: 'IndiGo', iata: '6E' },
  { name: 'Japan Airlines', iata: 'JL' },
  { name: 'Qantas', iata: 'QF' },
  { name: 'KLM Royal Dutch Airlines', iata: 'KL' },
  { name: 'Etihad Airways', iata: 'EY' },
  { name: 'China Southern Airlines', iata: 'CZ' },
  { name: 'China Eastern Airlines', iata: 'MU' },
  { name: 'Ryanair', iata: 'FR' },
];

export type Airport = {
  id: string;
  name: string;
  shortCode: string;
  airportLocation: string;
};

type AirportState = {
  airports: Airport[];
  isLoading: boolean;
  hasLoaded: boolean;
  error: string | null;
  fetchAirports: () => Promise<void>;
};

export const useAirportStore = create<AirportState>()((set, get) => ({
  airports: [],
  isLoading: false,
  hasLoaded: false,
  error: null,
  fetchAirports: async () => {
    // If airports are already loaded, don't fetch again
    if (get().hasLoaded && get().airports.length > 0) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const airportsResponse = await databases.listDocuments(
        Env.COLLECTION_ID,
        Env.COLLECTION_AIRPORTS,
        [
          Query.limit(100_000),
          Query.orderAsc('name'),
          Query.isNull('deletedAt'),
        ]
      );

      const mappedAirports = airportsResponse.documents.map((item) => ({
        id: item.$id,
        name: item.name,
        shortCode: item.shortCode,
        airportLocation: `${item.locationCity?.name} , ${item.locationCity?.state}`,
      }));

      set({
        airports: mappedAirports,
        isLoading: false,
        hasLoaded: true,
      });
    } catch (err) {
      console.error('Error fetching airports:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to fetch airports',
        isLoading: false,
      });
    }
  },
}));
