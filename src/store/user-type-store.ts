import { type Models } from 'react-native-appwrite';
import { create } from 'zustand';

export enum UserType {
  TRAVELLER = 'TRAVELLER',
  COMPANION = 'COMPANION',
}

export type IUserTypeStore = {
  userType: UserType;
  user: Models.User<any> | null;
  changeUserType: (type: UserType) => void;
  changeUser: (user: Models.User<any>) => void;
};
export const userTypeStore = create<IUserTypeStore>()((set, get) => ({
  userType: UserType.TRAVELLER,
  user: null,
  changeUserType: (type: UserType) => {
    set({ userType: type });
  },
  changeUser: (user: Models.User<any>) => {
    set({ user });
  },
}));
