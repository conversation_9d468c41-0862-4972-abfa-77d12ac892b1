import { account, databases } from '@/_lib/appwrite';
import { create } from 'zustand';
export type ILanguageStore = {
  languageThatTravellerSpeaks: { id: string; name: string }[];
  addLanguage: (id: string, name: string) => void;
  removeLanguage: (id: string, name: string) => void;
  emptyLanguage: () => void;
};
export const useLanguageStore = create<ILanguageStore>()((set, get) => ({
  languageThatTravellerSpeaks: [],

  addLanguage: (id: string, name: string) => {
    set((state) => {
      //check if the language already exists
      const languageExits = state.languageThatTravellerSpeaks.some(
        (language) => language.id === id,
      );

      if (languageExits) {
        return state;
      }

      return {
        languageThatTravellerSpeaks: [
          ...state.languageThatTravellerSpeaks,
          { id, name },
        ],
      };
    });    
  },

  removeLanguage: (id: string, name: string) => {
    set((state) => ({
      languageThatTravellerSpeaks: state.languageThatTravellerSpeaks.filter(
        (lang) => lang.id != id,
      ),
    }));
  },

  emptyLanguage: () => {
    set({ languageThatTravellerSpeaks: [] });
  },
  
})

);
